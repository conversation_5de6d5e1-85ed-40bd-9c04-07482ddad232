#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def analyze_symbol_pattern():
    """分析luckygold.jsonl中每列数据的Symbol6间隔规律"""
    
    # 统计数据
    total_records = 0
    symbol6_pattern_count = 0
    non_symbol6_pattern_count = 0
    
    # 详细分析结果
    pattern_analysis = {
        'perfect_symbol6_interval': 0,  # 完全符合Symbol6间隔的列数
        'partial_symbol6_interval': 0,  # 部分符合Symbol6间隔的列数
        'no_symbol6_interval': 0,       # 不符合Symbol6间隔的列数
        'empty_positions': 0,           # 空位置数量
        'total_columns': 0              # 总列数
    }
    
    # 读取文件并分析
    with open('luckygold.jsonl', 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if not line.strip():
                continue
                
            try:
                data = json.loads(line)
                plate_data = data.get('data', {}).get('plate', [])
                
                if not plate_data:
                    continue
                    
                total_records += 1
                columns = plate_data[0].get('column', [])
                
                print(f"\n=== 记录 {line_num} ===")
                
                for col_idx, column in enumerate(columns):
                    pattern_analysis['total_columns'] += 1
                    rows = column.get('row', [])
                    
                    # 提取每行的符号
                    symbols = []
                    for row in rows:
                        if not row:  # 空对象 {}
                            symbols.append('EMPTY')
                            pattern_analysis['empty_positions'] += 1
                        else:
                            symbol = row.get('symbol', 'UNKNOWN')
                            symbols.append(symbol)
                    
                    print(f"列 {col_idx}: {symbols}")
                    
                    # 分析Symbol6间隔规律
                    symbol6_positions = []
                    for i, symbol in enumerate(symbols):
                        if symbol == 'Symbol6':
                            symbol6_positions.append(i)
                    
                    # 检查是否符合间隔规律
                    if len(symbol6_positions) >= 2:
                        # 检查Symbol6是否在偶数位置 (0, 2, 4) 或奇数位置 (1, 3)
                        even_positions = [0, 2, 4]
                        odd_positions = [1, 3]
                        
                        symbol6_in_even = all(pos in even_positions for pos in symbol6_positions)
                        symbol6_in_odd = all(pos in odd_positions for pos in symbol6_positions)
                        
                        if symbol6_in_even or symbol6_in_odd:
                            # 检查间隔是否规律
                            intervals = []
                            for i in range(1, len(symbol6_positions)):
                                intervals.append(symbol6_positions[i] - symbol6_positions[i-1])
                            
                            # 如果间隔都是2，说明是完美的间隔模式
                            if all(interval == 2 for interval in intervals):
                                pattern_analysis['perfect_symbol6_interval'] += 1
                                print(f"  ✓ 完美Symbol6间隔模式: {symbol6_positions}")
                            else:
                                pattern_analysis['partial_symbol6_interval'] += 1
                                print(f"  ~ 部分Symbol6间隔模式: {symbol6_positions}, 间隔: {intervals}")
                        else:
                            pattern_analysis['no_symbol6_interval'] += 1
                            print(f"  ✗ 无Symbol6间隔模式: {symbol6_positions}")
                    else:
                        pattern_analysis['no_symbol6_interval'] += 1
                        print(f"  ✗ Symbol6数量不足: {symbol6_positions}")
                
                # 只分析前10条记录作为示例
                if total_records >= 10:
                    break
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析错误 (行 {line_num}): {e}")
                continue
            except Exception as e:
                print(f"处理错误 (行 {line_num}): {e}")
                continue
    
    # 输出统计结果
    print(f"\n=== 统计结果 ===")
    print(f"总记录数: {total_records}")
    print(f"总列数: {pattern_analysis['total_columns']}")
    print(f"完美Symbol6间隔列数: {pattern_analysis['perfect_symbol6_interval']}")
    print(f"部分Symbol6间隔列数: {pattern_analysis['partial_symbol6_interval']}")
    print(f"无Symbol6间隔列数: {pattern_analysis['no_symbol6_interval']}")
    print(f"空位置数量: {pattern_analysis['empty_positions']}")
    
    # 计算百分比
    if pattern_analysis['total_columns'] > 0:
        perfect_rate = (pattern_analysis['perfect_symbol6_interval'] / pattern_analysis['total_columns']) * 100
        partial_rate = (pattern_analysis['partial_symbol6_interval'] / pattern_analysis['total_columns']) * 100
        no_pattern_rate = (pattern_analysis['no_symbol6_interval'] / pattern_analysis['total_columns']) * 100
        
        print(f"\n=== 百分比统计 ===")
        print(f"完美Symbol6间隔比例: {perfect_rate:.1f}%")
        print(f"部分Symbol6间隔比例: {partial_rate:.1f}%")
        print(f"无Symbol6间隔比例: {no_pattern_rate:.1f}%")

if __name__ == "__main__":
    analyze_symbol_pattern()
