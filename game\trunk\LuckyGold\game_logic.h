#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_ODDS_CONFIG_COUNT 11       	//转盘中奖分布
#define ARRAY_1             3
#define ARRAY_2             3
/* 牌面值 */
enum enumIcon
{
	Wild = 0,	//对应图标 wild2X
	Bonus = 1, //对应图标 浅色bonus(一般游戏中的红利金砖)一般游戏转出5个以上红利金砖可开启点石成金特色游戏
	Symbol1 = 2, //对应图标 黄金7
	Symbol2 = 3, //对应图标 银色7
	Symbol3 = 4, //对应图标 BAR*3
	Symbol4 = 5, //对应图标 BAR*2
	Symbol5 = 6, //对应图标 BAR
	Symbol6 = 7, //对应图标 空白
	Mix7 = 8, //对应图标 wild2X
	MixBar = 9 //对应图标 深色bonus (点石成金中的带分金砖)
};

//const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
//	6, 12, 30, 60, 90, 150, 300, 450, 900, 2100, 3000, 4500, 6000, 9600, 19200
//};
//单位元
const double bet_config_count[MAX_BET_CONFIG_COUNT] = {
	1, 2, 3, 5, 8, 10, 20, 50, 100, 200, 300, 400, 500, 700, 1000
};



// 以下赔率数组下标对应次数，数据数组对应赔率

//Symbol1 赔率5 X 6, 4 X 1.8, 3 X 0.6, 2 X 0.2
const double Symbol1_odd[6] = { 0, 0, 0.2, 0.6, 1.8, 6 };
//Symbol2 赔率5 X 5, 4 X 1.5, 3 X 0.5, 2 X 0.2		
const double Symbol2_odd[6] = {0, 0, 0.2, 0.5, 1.5, 5 };
//任意 symbol1+symbol2 5 X 3, 4 X 0.9,3 X 0.3
const double Symbol1_2_odd[6] = { 0, 0, 0, 0.3, 0.9, 3 };
//Symbol3
const double Symbol3_odd[6] = { 0, 0, 0, 0.4, 1.2, 4 };
//Symbol4
const double Symbol4_odd[6] = { 0, 0, 0, 0.3, 0.9, 3 };
//Symbol5
const double Symbol5_odd[6] = { 0, 0, 0, 0.2, 0.6, 2 };
//任意 symbol3+symbol4+symbol5 
const double Symbol3_4_odd[6] = { 0, 0, 0, 0.1, 0.3, 1 };




struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int status; // 游戏状态 1-已经初始化金币 0-未初始化
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
	int round_result;//当前回合奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式 1-15
	int mul;//倍数
	int free_count;//free次数
	int bonus;//总奖励
	int light;

	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

	vector<int> graph_data;

	int award_type;
	int plate_win;
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info()
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		record_id = 0;
		award_type = 0;

		graph_data.clear();

	}
	void reset() 
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		award_type = 0;
		free_count = 0;
		mul = 0;
		light = 0;
		graph_data.clear();
	}

};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
	static game_logic *GetInstance()
	{
		static game_logic instance;
		return &instance;
	}
    void init();

	_uint64 gen_new_round_index();

	/*计算连线 tempIcons图标位置 vec_line返回可组成连线的数据*/
	void CalcResultTimes(user_game_info &game_info);
	int CalcResultTimes(user_game_info &game_info, int icons[3][3], int pageNum);
	/*随机一个不中奖的结果*/
	void RandFillIconNoWin(user_game_info &game_info);
	//解析数据
	bool get_game_data_from_graph_data(user_game_info &game_info, const string& graph);

private:
	int getMul(int count, int num);
};

#endif
