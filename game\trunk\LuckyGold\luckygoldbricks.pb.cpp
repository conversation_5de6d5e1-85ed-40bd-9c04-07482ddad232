// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: luckygoldbricks.proto

#include "luckygoldbricks.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace luckygoldProto {
PROTOBUF_CONSTEXPR Block::Block(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.row_)*/0u
  , /*decltype(_impl_.column_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct BlockDefaultTypeInternal {
  PROTOBUF_CONSTEXPR BlockDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~BlockDefaultTypeInternal() {}
  union {
    Block _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 BlockDefaultTypeInternal _Block_default_instance_;
PROTOBUF_CONSTEXPR Cell::Cell(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.symbol_)*/0
  , /*decltype(_impl_.isconnect_)*/false
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CellDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CellDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CellDefaultTypeInternal() {}
  union {
    Cell _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CellDefaultTypeInternal _Cell_default_instance_;
PROTOBUF_CONSTEXPR ColumnData::ColumnData(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.row_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ColumnDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ColumnDataDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ColumnDataDefaultTypeInternal() {}
  union {
    ColumnData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ColumnDataDefaultTypeInternal _ColumnData_default_instance_;
PROTOBUF_CONSTEXPR Plate::Plate(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.column_)*/{}
  , /*decltype(_impl_.gold_)*/nullptr
  , /*decltype(_impl_.jp_)*/nullptr
  , /*decltype(_impl_.win_)*/0
  , /*decltype(_impl_.goldwin_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PlateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PlateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PlateDefaultTypeInternal() {}
  union {
    Plate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PlateDefaultTypeInternal _Plate_default_instance_;
PROTOBUF_CONSTEXPR BonusInfo::BonusInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.gold_)*/{}
  , /*decltype(_impl_.barwin_)*/0
  , /*decltype(_impl_.count_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct BonusInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR BonusInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~BonusInfoDefaultTypeInternal() {}
  union {
    BonusInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 BonusInfoDefaultTypeInternal _BonusInfo_default_instance_;
PROTOBUF_CONSTEXPR JpInfo::JpInfo(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.win_)*/0
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct JpInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR JpInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~JpInfoDefaultTypeInternal() {}
  union {
    JpInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 JpInfoDefaultTypeInternal _JpInfo_default_instance_;
PROTOBUF_CONSTEXPR JpCurrency::JpCurrency(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.info_)*/{}
  , /*decltype(_impl_.currency_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct JpCurrencyDefaultTypeInternal {
  PROTOBUF_CONSTEXPR JpCurrencyDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~JpCurrencyDefaultTypeInternal() {}
  union {
    JpCurrency _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 JpCurrencyDefaultTypeInternal _JpCurrency_default_instance_;
PROTOBUF_CONSTEXPR AllPlate::AllPlate(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.plate_)*/{}
  , /*decltype(_impl_.jp_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct AllPlateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AllPlateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AllPlateDefaultTypeInternal() {}
  union {
    AllPlate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AllPlateDefaultTypeInternal _AllPlate_default_instance_;
}  // namespace luckygoldProto
static ::_pb::Metadata file_level_metadata_luckygoldbricks_2eproto[8];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_luckygoldbricks_2eproto[1];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_luckygoldbricks_2eproto = nullptr;

const uint32_t TableStruct_luckygoldbricks_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Block, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Block, _impl_.row_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Block, _impl_.column_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Cell, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Cell, _impl_.symbol_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Cell, _impl_.isconnect_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::ColumnData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::ColumnData, _impl_.row_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _impl_.column_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _impl_.win_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _impl_.gold_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _impl_.goldwin_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::Plate, _impl_.jp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::BonusInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::BonusInfo, _impl_.barwin_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::BonusInfo, _impl_.count_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::BonusInfo, _impl_.gold_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpInfo, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpInfo, _impl_.win_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpCurrency, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpCurrency, _impl_.currency_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::JpCurrency, _impl_.info_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::AllPlate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::AllPlate, _impl_.plate_),
  PROTOBUF_FIELD_OFFSET(::luckygoldProto::AllPlate, _impl_.jp_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::luckygoldProto::Block)},
  { 8, -1, -1, sizeof(::luckygoldProto::Cell)},
  { 16, -1, -1, sizeof(::luckygoldProto::ColumnData)},
  { 23, -1, -1, sizeof(::luckygoldProto::Plate)},
  { 34, -1, -1, sizeof(::luckygoldProto::BonusInfo)},
  { 43, -1, -1, sizeof(::luckygoldProto::JpInfo)},
  { 51, -1, -1, sizeof(::luckygoldProto::JpCurrency)},
  { 59, -1, -1, sizeof(::luckygoldProto::AllPlate)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::luckygoldProto::_Block_default_instance_._instance,
  &::luckygoldProto::_Cell_default_instance_._instance,
  &::luckygoldProto::_ColumnData_default_instance_._instance,
  &::luckygoldProto::_Plate_default_instance_._instance,
  &::luckygoldProto::_BonusInfo_default_instance_._instance,
  &::luckygoldProto::_JpInfo_default_instance_._instance,
  &::luckygoldProto::_JpCurrency_default_instance_._instance,
  &::luckygoldProto::_AllPlate_default_instance_._instance,
};

const char descriptor_table_protodef_luckygoldbricks_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\025luckygoldbricks.proto\022\016luckygoldProto\""
  "$\n\005Block\022\013\n\003row\030\001 \001(\r\022\016\n\006column\030\002 \001(\r\"A\n"
  "\004Cell\022&\n\006symbol\030\001 \001(\0162\026.luckygoldProto.S"
  "ymbol\022\021\n\tisConnect\030\002 \001(\010\"/\n\nColumnData\022!"
  "\n\003row\030\001 \003(\0132\024.luckygoldProto.Cell\"\236\001\n\005Pl"
  "ate\022*\n\006column\030\001 \003(\0132\032.luckygoldProto.Col"
  "umnData\022\013\n\003win\030\002 \001(\001\022\'\n\004gold\030\003 \001(\0132\031.luc"
  "kygoldProto.BonusInfo\022\017\n\007goldWin\030\004 \001(\001\022\""
  "\n\002Jp\030\005 \001(\0132\026.luckygoldProto.JpInfo\"8\n\tBo"
  "nusInfo\022\016\n\006barWin\030\001 \001(\001\022\r\n\005count\030\002 \001(\003\022\014"
  "\n\004gold\030\003 \003(\001\"!\n\006JpInfo\022\n\n\002id\030\001 \001(\005\022\013\n\003wi"
  "n\030\002 \001(\001\"D\n\nJpCurrency\022\020\n\010currency\030\001 \001(\005\022"
  "$\n\004info\030\002 \003(\0132\026.luckygoldProto.JpInfo\"X\n"
  "\010AllPlate\022$\n\005plate\030\001 \003(\0132\025.luckygoldProt"
  "o.Plate\022&\n\002jp\030\002 \003(\0132\032.luckygoldProto.JpC"
  "urrency*\201\001\n\006Symbol\022\010\n\004Wild\020\000\022\t\n\005Bonus\020\001\022"
  "\013\n\007Symbol1\020\002\022\013\n\007Symbol2\020\003\022\013\n\007Symbol3\020\004\022\013"
  "\n\007Symbol4\020\005\022\013\n\007Symbol5\020\006\022\013\n\007Symbol6\020\007\022\010\n"
  "\004Mix7\020\010\022\n\n\006MixBar\020\tb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_luckygoldbricks_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_luckygoldbricks_2eproto = {
    false, false, 747, descriptor_table_protodef_luckygoldbricks_2eproto,
    "luckygoldbricks.proto",
    &descriptor_table_luckygoldbricks_2eproto_once, nullptr, 0, 8,
    schemas, file_default_instances, TableStruct_luckygoldbricks_2eproto::offsets,
    file_level_metadata_luckygoldbricks_2eproto, file_level_enum_descriptors_luckygoldbricks_2eproto,
    file_level_service_descriptors_luckygoldbricks_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_luckygoldbricks_2eproto_getter() {
  return &descriptor_table_luckygoldbricks_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_luckygoldbricks_2eproto(&descriptor_table_luckygoldbricks_2eproto);
namespace luckygoldProto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Symbol_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_luckygoldbricks_2eproto);
  return file_level_enum_descriptors_luckygoldbricks_2eproto[0];
}
bool Symbol_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class Block::_Internal {
 public:
};

Block::Block(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.Block)
}
Block::Block(const Block& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Block* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.row_){}
    , decltype(_impl_.column_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.row_, &from._impl_.row_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.column_) -
    reinterpret_cast<char*>(&_impl_.row_)) + sizeof(_impl_.column_));
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.Block)
}

inline void Block::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.row_){0u}
    , decltype(_impl_.column_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Block::~Block() {
  // @@protoc_insertion_point(destructor:luckygoldProto.Block)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Block::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Block::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Block::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.Block)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.row_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.column_) -
      reinterpret_cast<char*>(&_impl_.row_)) + sizeof(_impl_.column_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Block::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.row_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 column = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.column_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Block::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.Block)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row = 1;
  if (this->_internal_row() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row(), target);
  }

  // uint32 column = 2;
  if (this->_internal_column() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_column(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.Block)
  return target;
}

size_t Block::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.Block)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 row = 1;
  if (this->_internal_row() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_row());
  }

  // uint32 column = 2;
  if (this->_internal_column() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_column());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Block::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Block::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Block::GetClassData() const { return &_class_data_; }


void Block::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Block*>(&to_msg);
  auto& from = static_cast<const Block&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.Block)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row() != 0) {
    _this->_internal_set_row(from._internal_row());
  }
  if (from._internal_column() != 0) {
    _this->_internal_set_column(from._internal_column());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Block::CopyFrom(const Block& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.Block)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Block::IsInitialized() const {
  return true;
}

void Block::InternalSwap(Block* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Block, _impl_.column_)
      + sizeof(Block::_impl_.column_)
      - PROTOBUF_FIELD_OFFSET(Block, _impl_.row_)>(
          reinterpret_cast<char*>(&_impl_.row_),
          reinterpret_cast<char*>(&other->_impl_.row_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Block::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[0]);
}

// ===================================================================

class Cell::_Internal {
 public:
};

Cell::Cell(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.Cell)
}
Cell::Cell(const Cell& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Cell* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.symbol_){}
    , decltype(_impl_.isconnect_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.symbol_, &from._impl_.symbol_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.isconnect_) -
    reinterpret_cast<char*>(&_impl_.symbol_)) + sizeof(_impl_.isconnect_));
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.Cell)
}

inline void Cell::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.symbol_){0}
    , decltype(_impl_.isconnect_){false}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Cell::~Cell() {
  // @@protoc_insertion_point(destructor:luckygoldProto.Cell)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Cell::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Cell::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Cell::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.Cell)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.symbol_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.isconnect_) -
      reinterpret_cast<char*>(&_impl_.symbol_)) + sizeof(_impl_.isconnect_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Cell::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .luckygoldProto.Symbol symbol = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_symbol(static_cast<::luckygoldProto::Symbol>(val));
        } else
          goto handle_unusual;
        continue;
      // bool isConnect = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.isconnect_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Cell::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.Cell)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .luckygoldProto.Symbol symbol = 1;
  if (this->_internal_symbol() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_symbol(), target);
  }

  // bool isConnect = 2;
  if (this->_internal_isconnect() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_isconnect(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.Cell)
  return target;
}

size_t Cell::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.Cell)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .luckygoldProto.Symbol symbol = 1;
  if (this->_internal_symbol() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_symbol());
  }

  // bool isConnect = 2;
  if (this->_internal_isconnect() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Cell::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Cell::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Cell::GetClassData() const { return &_class_data_; }


void Cell::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Cell*>(&to_msg);
  auto& from = static_cast<const Cell&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.Cell)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_symbol() != 0) {
    _this->_internal_set_symbol(from._internal_symbol());
  }
  if (from._internal_isconnect() != 0) {
    _this->_internal_set_isconnect(from._internal_isconnect());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Cell::CopyFrom(const Cell& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.Cell)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Cell::IsInitialized() const {
  return true;
}

void Cell::InternalSwap(Cell* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Cell, _impl_.isconnect_)
      + sizeof(Cell::_impl_.isconnect_)
      - PROTOBUF_FIELD_OFFSET(Cell, _impl_.symbol_)>(
          reinterpret_cast<char*>(&_impl_.symbol_),
          reinterpret_cast<char*>(&other->_impl_.symbol_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Cell::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[1]);
}

// ===================================================================

class ColumnData::_Internal {
 public:
};

ColumnData::ColumnData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.ColumnData)
}
ColumnData::ColumnData(const ColumnData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ColumnData* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.row_){from._impl_.row_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.ColumnData)
}

inline void ColumnData::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.row_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

ColumnData::~ColumnData() {
  // @@protoc_insertion_point(destructor:luckygoldProto.ColumnData)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ColumnData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.row_.~RepeatedPtrField();
}

void ColumnData::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ColumnData::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.ColumnData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.row_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ColumnData::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .luckygoldProto.Cell row = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_row(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ColumnData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.ColumnData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .luckygoldProto.Cell row = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_row_size()); i < n; i++) {
    const auto& repfield = this->_internal_row(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.ColumnData)
  return target;
}

size_t ColumnData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.ColumnData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .luckygoldProto.Cell row = 1;
  total_size += 1UL * this->_internal_row_size();
  for (const auto& msg : this->_impl_.row_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ColumnData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ColumnData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ColumnData::GetClassData() const { return &_class_data_; }


void ColumnData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ColumnData*>(&to_msg);
  auto& from = static_cast<const ColumnData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.ColumnData)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.row_.MergeFrom(from._impl_.row_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ColumnData::CopyFrom(const ColumnData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.ColumnData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ColumnData::IsInitialized() const {
  return true;
}

void ColumnData::InternalSwap(ColumnData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.row_.InternalSwap(&other->_impl_.row_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ColumnData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[2]);
}

// ===================================================================

class Plate::_Internal {
 public:
  static const ::luckygoldProto::BonusInfo& gold(const Plate* msg);
  static const ::luckygoldProto::JpInfo& jp(const Plate* msg);
};

const ::luckygoldProto::BonusInfo&
Plate::_Internal::gold(const Plate* msg) {
  return *msg->_impl_.gold_;
}
const ::luckygoldProto::JpInfo&
Plate::_Internal::jp(const Plate* msg) {
  return *msg->_impl_.jp_;
}
Plate::Plate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.Plate)
}
Plate::Plate(const Plate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Plate* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.column_){from._impl_.column_}
    , decltype(_impl_.gold_){nullptr}
    , decltype(_impl_.jp_){nullptr}
    , decltype(_impl_.win_){}
    , decltype(_impl_.goldwin_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_gold()) {
    _this->_impl_.gold_ = new ::luckygoldProto::BonusInfo(*from._impl_.gold_);
  }
  if (from._internal_has_jp()) {
    _this->_impl_.jp_ = new ::luckygoldProto::JpInfo(*from._impl_.jp_);
  }
  ::memcpy(&_impl_.win_, &from._impl_.win_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.goldwin_) -
    reinterpret_cast<char*>(&_impl_.win_)) + sizeof(_impl_.goldwin_));
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.Plate)
}

inline void Plate::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.column_){arena}
    , decltype(_impl_.gold_){nullptr}
    , decltype(_impl_.jp_){nullptr}
    , decltype(_impl_.win_){0}
    , decltype(_impl_.goldwin_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Plate::~Plate() {
  // @@protoc_insertion_point(destructor:luckygoldProto.Plate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Plate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.column_.~RepeatedPtrField();
  if (this != internal_default_instance()) delete _impl_.gold_;
  if (this != internal_default_instance()) delete _impl_.jp_;
}

void Plate::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Plate::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.Plate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.column_.Clear();
  if (GetArenaForAllocation() == nullptr && _impl_.gold_ != nullptr) {
    delete _impl_.gold_;
  }
  _impl_.gold_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.jp_ != nullptr) {
    delete _impl_.jp_;
  }
  _impl_.jp_ = nullptr;
  ::memset(&_impl_.win_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.goldwin_) -
      reinterpret_cast<char*>(&_impl_.win_)) + sizeof(_impl_.goldwin_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Plate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .luckygoldProto.ColumnData column = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_column(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // double win = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.win_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .luckygoldProto.BonusInfo gold = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_gold(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double goldWin = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _impl_.goldwin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .luckygoldProto.JpInfo Jp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_jp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Plate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.Plate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .luckygoldProto.ColumnData column = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_column_size()); i < n; i++) {
    const auto& repfield = this->_internal_column(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // double win = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = this->_internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_win(), target);
  }

  // .luckygoldProto.BonusInfo gold = 3;
  if (this->_internal_has_gold()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::gold(this),
        _Internal::gold(this).GetCachedSize(), target, stream);
  }

  // double goldWin = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_goldwin = this->_internal_goldwin();
  uint64_t raw_goldwin;
  memcpy(&raw_goldwin, &tmp_goldwin, sizeof(tmp_goldwin));
  if (raw_goldwin != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(4, this->_internal_goldwin(), target);
  }

  // .luckygoldProto.JpInfo Jp = 5;
  if (this->_internal_has_jp()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::jp(this),
        _Internal::jp(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.Plate)
  return target;
}

size_t Plate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.Plate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .luckygoldProto.ColumnData column = 1;
  total_size += 1UL * this->_internal_column_size();
  for (const auto& msg : this->_impl_.column_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .luckygoldProto.BonusInfo gold = 3;
  if (this->_internal_has_gold()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.gold_);
  }

  // .luckygoldProto.JpInfo Jp = 5;
  if (this->_internal_has_jp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.jp_);
  }

  // double win = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = this->_internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    total_size += 1 + 8;
  }

  // double goldWin = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_goldwin = this->_internal_goldwin();
  uint64_t raw_goldwin;
  memcpy(&raw_goldwin, &tmp_goldwin, sizeof(tmp_goldwin));
  if (raw_goldwin != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Plate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Plate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Plate::GetClassData() const { return &_class_data_; }


void Plate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Plate*>(&to_msg);
  auto& from = static_cast<const Plate&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.Plate)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.column_.MergeFrom(from._impl_.column_);
  if (from._internal_has_gold()) {
    _this->_internal_mutable_gold()->::luckygoldProto::BonusInfo::MergeFrom(
        from._internal_gold());
  }
  if (from._internal_has_jp()) {
    _this->_internal_mutable_jp()->::luckygoldProto::JpInfo::MergeFrom(
        from._internal_jp());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = from._internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    _this->_internal_set_win(from._internal_win());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_goldwin = from._internal_goldwin();
  uint64_t raw_goldwin;
  memcpy(&raw_goldwin, &tmp_goldwin, sizeof(tmp_goldwin));
  if (raw_goldwin != 0) {
    _this->_internal_set_goldwin(from._internal_goldwin());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Plate::CopyFrom(const Plate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.Plate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Plate::IsInitialized() const {
  return true;
}

void Plate::InternalSwap(Plate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.column_.InternalSwap(&other->_impl_.column_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Plate, _impl_.goldwin_)
      + sizeof(Plate::_impl_.goldwin_)
      - PROTOBUF_FIELD_OFFSET(Plate, _impl_.gold_)>(
          reinterpret_cast<char*>(&_impl_.gold_),
          reinterpret_cast<char*>(&other->_impl_.gold_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Plate::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[3]);
}

// ===================================================================

class BonusInfo::_Internal {
 public:
};

BonusInfo::BonusInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.BonusInfo)
}
BonusInfo::BonusInfo(const BonusInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  BonusInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.gold_){from._impl_.gold_}
    , decltype(_impl_.barwin_){}
    , decltype(_impl_.count_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.barwin_, &from._impl_.barwin_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.count_) -
    reinterpret_cast<char*>(&_impl_.barwin_)) + sizeof(_impl_.count_));
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.BonusInfo)
}

inline void BonusInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.gold_){arena}
    , decltype(_impl_.barwin_){0}
    , decltype(_impl_.count_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

BonusInfo::~BonusInfo() {
  // @@protoc_insertion_point(destructor:luckygoldProto.BonusInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void BonusInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.gold_.~RepeatedField();
}

void BonusInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void BonusInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.BonusInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.gold_.Clear();
  ::memset(&_impl_.barwin_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.count_) -
      reinterpret_cast<char*>(&_impl_.barwin_)) + sizeof(_impl_.count_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BonusInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double barWin = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.barwin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // int64 count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated double gold = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_gold(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 25) {
          _internal_add_gold(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BonusInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.BonusInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double barWin = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_barwin = this->_internal_barwin();
  uint64_t raw_barwin;
  memcpy(&raw_barwin, &tmp_barwin, sizeof(tmp_barwin));
  if (raw_barwin != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_barwin(), target);
  }

  // int64 count = 2;
  if (this->_internal_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(2, this->_internal_count(), target);
  }

  // repeated double gold = 3;
  if (this->_internal_gold_size() > 0) {
    target = stream->WriteFixedPacked(3, _internal_gold(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.BonusInfo)
  return target;
}

size_t BonusInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.BonusInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated double gold = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_gold_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // double barWin = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_barwin = this->_internal_barwin();
  uint64_t raw_barwin;
  memcpy(&raw_barwin, &tmp_barwin, sizeof(tmp_barwin));
  if (raw_barwin != 0) {
    total_size += 1 + 8;
  }

  // int64 count = 2;
  if (this->_internal_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BonusInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    BonusInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BonusInfo::GetClassData() const { return &_class_data_; }


void BonusInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<BonusInfo*>(&to_msg);
  auto& from = static_cast<const BonusInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.BonusInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.gold_.MergeFrom(from._impl_.gold_);
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_barwin = from._internal_barwin();
  uint64_t raw_barwin;
  memcpy(&raw_barwin, &tmp_barwin, sizeof(tmp_barwin));
  if (raw_barwin != 0) {
    _this->_internal_set_barwin(from._internal_barwin());
  }
  if (from._internal_count() != 0) {
    _this->_internal_set_count(from._internal_count());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BonusInfo::CopyFrom(const BonusInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.BonusInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BonusInfo::IsInitialized() const {
  return true;
}

void BonusInfo::InternalSwap(BonusInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.gold_.InternalSwap(&other->_impl_.gold_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BonusInfo, _impl_.count_)
      + sizeof(BonusInfo::_impl_.count_)
      - PROTOBUF_FIELD_OFFSET(BonusInfo, _impl_.barwin_)>(
          reinterpret_cast<char*>(&_impl_.barwin_),
          reinterpret_cast<char*>(&other->_impl_.barwin_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BonusInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[4]);
}

// ===================================================================

class JpInfo::_Internal {
 public:
};

JpInfo::JpInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.JpInfo)
}
JpInfo::JpInfo(const JpInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  JpInfo* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.win_){}
    , decltype(_impl_.id_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.win_, &from._impl_.win_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.id_) -
    reinterpret_cast<char*>(&_impl_.win_)) + sizeof(_impl_.id_));
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.JpInfo)
}

inline void JpInfo::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.win_){0}
    , decltype(_impl_.id_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

JpInfo::~JpInfo() {
  // @@protoc_insertion_point(destructor:luckygoldProto.JpInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void JpInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void JpInfo::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void JpInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.JpInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.win_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.id_) -
      reinterpret_cast<char*>(&_impl_.win_)) + sizeof(_impl_.id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JpInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double win = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.win_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JpInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.JpInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // double win = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = this->_internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_win(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.JpInfo)
  return target;
}

size_t JpInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.JpInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double win = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = this->_internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    total_size += 1 + 8;
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JpInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    JpInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JpInfo::GetClassData() const { return &_class_data_; }


void JpInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<JpInfo*>(&to_msg);
  auto& from = static_cast<const JpInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.JpInfo)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_win = from._internal_win();
  uint64_t raw_win;
  memcpy(&raw_win, &tmp_win, sizeof(tmp_win));
  if (raw_win != 0) {
    _this->_internal_set_win(from._internal_win());
  }
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JpInfo::CopyFrom(const JpInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.JpInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JpInfo::IsInitialized() const {
  return true;
}

void JpInfo::InternalSwap(JpInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(JpInfo, _impl_.id_)
      + sizeof(JpInfo::_impl_.id_)
      - PROTOBUF_FIELD_OFFSET(JpInfo, _impl_.win_)>(
          reinterpret_cast<char*>(&_impl_.win_),
          reinterpret_cast<char*>(&other->_impl_.win_));
}

::PROTOBUF_NAMESPACE_ID::Metadata JpInfo::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[5]);
}

// ===================================================================

class JpCurrency::_Internal {
 public:
};

JpCurrency::JpCurrency(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.JpCurrency)
}
JpCurrency::JpCurrency(const JpCurrency& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  JpCurrency* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.info_){from._impl_.info_}
    , decltype(_impl_.currency_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.currency_ = from._impl_.currency_;
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.JpCurrency)
}

inline void JpCurrency::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.info_){arena}
    , decltype(_impl_.currency_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

JpCurrency::~JpCurrency() {
  // @@protoc_insertion_point(destructor:luckygoldProto.JpCurrency)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void JpCurrency::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.info_.~RepeatedPtrField();
}

void JpCurrency::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void JpCurrency::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.JpCurrency)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.info_.Clear();
  _impl_.currency_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JpCurrency::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 currency = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.currency_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .luckygoldProto.JpInfo info = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JpCurrency::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.JpCurrency)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 currency = 1;
  if (this->_internal_currency() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_currency(), target);
  }

  // repeated .luckygoldProto.JpInfo info = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_info_size()); i < n; i++) {
    const auto& repfield = this->_internal_info(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.JpCurrency)
  return target;
}

size_t JpCurrency::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.JpCurrency)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .luckygoldProto.JpInfo info = 2;
  total_size += 1UL * this->_internal_info_size();
  for (const auto& msg : this->_impl_.info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 currency = 1;
  if (this->_internal_currency() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_currency());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JpCurrency::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    JpCurrency::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JpCurrency::GetClassData() const { return &_class_data_; }


void JpCurrency::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<JpCurrency*>(&to_msg);
  auto& from = static_cast<const JpCurrency&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.JpCurrency)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.info_.MergeFrom(from._impl_.info_);
  if (from._internal_currency() != 0) {
    _this->_internal_set_currency(from._internal_currency());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JpCurrency::CopyFrom(const JpCurrency& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.JpCurrency)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JpCurrency::IsInitialized() const {
  return true;
}

void JpCurrency::InternalSwap(JpCurrency* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.info_.InternalSwap(&other->_impl_.info_);
  swap(_impl_.currency_, other->_impl_.currency_);
}

::PROTOBUF_NAMESPACE_ID::Metadata JpCurrency::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[6]);
}

// ===================================================================

class AllPlate::_Internal {
 public:
};

AllPlate::AllPlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:luckygoldProto.AllPlate)
}
AllPlate::AllPlate(const AllPlate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  AllPlate* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.plate_){from._impl_.plate_}
    , decltype(_impl_.jp_){from._impl_.jp_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:luckygoldProto.AllPlate)
}

inline void AllPlate::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.plate_){arena}
    , decltype(_impl_.jp_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

AllPlate::~AllPlate() {
  // @@protoc_insertion_point(destructor:luckygoldProto.AllPlate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AllPlate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.plate_.~RepeatedPtrField();
  _impl_.jp_.~RepeatedPtrField();
}

void AllPlate::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void AllPlate::Clear() {
// @@protoc_insertion_point(message_clear_start:luckygoldProto.AllPlate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.plate_.Clear();
  _impl_.jp_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AllPlate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .luckygoldProto.Plate plate = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_plate(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .luckygoldProto.JpCurrency jp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_jp(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AllPlate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:luckygoldProto.AllPlate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .luckygoldProto.Plate plate = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_plate_size()); i < n; i++) {
    const auto& repfield = this->_internal_plate(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .luckygoldProto.JpCurrency jp = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_jp_size()); i < n; i++) {
    const auto& repfield = this->_internal_jp(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:luckygoldProto.AllPlate)
  return target;
}

size_t AllPlate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:luckygoldProto.AllPlate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .luckygoldProto.Plate plate = 1;
  total_size += 1UL * this->_internal_plate_size();
  for (const auto& msg : this->_impl_.plate_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .luckygoldProto.JpCurrency jp = 2;
  total_size += 1UL * this->_internal_jp_size();
  for (const auto& msg : this->_impl_.jp_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AllPlate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    AllPlate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AllPlate::GetClassData() const { return &_class_data_; }


void AllPlate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<AllPlate*>(&to_msg);
  auto& from = static_cast<const AllPlate&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:luckygoldProto.AllPlate)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.plate_.MergeFrom(from._impl_.plate_);
  _this->_impl_.jp_.MergeFrom(from._impl_.jp_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AllPlate::CopyFrom(const AllPlate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:luckygoldProto.AllPlate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AllPlate::IsInitialized() const {
  return true;
}

void AllPlate::InternalSwap(AllPlate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.plate_.InternalSwap(&other->_impl_.plate_);
  _impl_.jp_.InternalSwap(&other->_impl_.jp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AllPlate::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_luckygoldbricks_2eproto_getter, &descriptor_table_luckygoldbricks_2eproto_once,
      file_level_metadata_luckygoldbricks_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace luckygoldProto
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::luckygoldProto::Block*
Arena::CreateMaybeMessage< ::luckygoldProto::Block >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::Block >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::Cell*
Arena::CreateMaybeMessage< ::luckygoldProto::Cell >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::Cell >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::ColumnData*
Arena::CreateMaybeMessage< ::luckygoldProto::ColumnData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::ColumnData >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::Plate*
Arena::CreateMaybeMessage< ::luckygoldProto::Plate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::Plate >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::BonusInfo*
Arena::CreateMaybeMessage< ::luckygoldProto::BonusInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::BonusInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::JpInfo*
Arena::CreateMaybeMessage< ::luckygoldProto::JpInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::JpInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::JpCurrency*
Arena::CreateMaybeMessage< ::luckygoldProto::JpCurrency >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::JpCurrency >(arena);
}
template<> PROTOBUF_NOINLINE ::luckygoldProto::AllPlate*
Arena::CreateMaybeMessage< ::luckygoldProto::AllPlate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::luckygoldProto::AllPlate >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
