
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <string.h>

#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "user_token.h"
#include "block_timeout_socket.h"
#include <iostream>
#include <fstream>
#include <chrono>
#include "winlose.h"
#include "google/protobuf/text_format.h"
#include "server.pb.h"
#include "nlohmann/json.hpp"
using json = nlohmann::json;
CDLLHelper<ICommon> g_com(SVRLIB_NAME, "_common_instance", "_common_free");

std::string &replace_all(string &str, const std::string &old_value, const std::string &new_value, int off)
{

	std::string::size_type pos(0);
	while (true)
	{
		pos = str.find(old_value, pos);
		if (pos != string::npos)
		{
			str.replace(pos, old_value.length(), new_value);
			pos += off;
		}
		else
		{
			break;
		}
	}
	return str;
}

int char2bits(char ch)
{

	int bits = 0;
	if (ch >= 'a' && ch <= 'z')
	{
		bits = ch - 'a' + 10;
	}
	else if (ch >= 'A' && ch <= 'Z')
	{
		bits = ch - 'A' + 10;
	}
	else if (ch >= '0' && ch <= '9')
	{
		bits = ch - '0';
	}
	else
	{
		bits = -1;
	}
	return bits;
}

_uint64 _parseInt(const char *a, int len)
{

	std::vector<int> numArr;
	int radix = 16;
	_uint64 result = 0;
	for (int i = 0; i < len; i++)
	{
		int num = char2bits(a[i]);
		numArr.push_back(num);
	}
	for (int i = 0; i < numArr.size(); i++)
	{
		result += numArr[i] * pow(radix, numArr.size() - i - 1);
	}
	return result;
}
void writeGraph(const user_game_info &info)
{
	// std::remove("graph.txt");
	std::ofstream ofs;
	ofs.open("graph_free.txt", ios::out | ios::app);

	Json::Value graph_root;
	Json::Value item;
	ofs.close();
}
//#define TEST_BET

int CGameComponent::init(ITableHunded *table, data_pool_interface *data_pool, gamecontrol_interface *game_control, const void *rule, int rule_len)
{
	if (!table || !data_pool)
		return -1;

	m_table = table;
	m_data_pool = data_pool;
	m_game_control = game_control;
	m_tax = 0;
	m_game_logic.init();
	analy_game_rule(rule, rule_len);

	// run_rtp();
#ifdef TEST_BET
	user_game_info info;
	info.cur_mode = 0;
	info.bet = 1000;
	m_map_user_game_info[201156] = info;
	
#if 0
	for (int i = 0; i < 100; i++)
	{
		calcu_graph(info, i);
		test_bet_roll_result(info, i);
	}
#else
	int t = 27;
	calcu_graph(info, t);
	test_bet_roll_result(info, t);
#endif
#endif
	set_timer(TID_CHECK_USER, 60 * 60 * 1000, 0, 0);

	return 0;
}

bool CGameComponent::analy_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL || rule_len == 0)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return false;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return false;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("analy_game_rule tax(%d)", m_tax);
	return true;
}

void CGameComponent::reset_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("reset_game_rule tax(%d)", m_tax);
	return;
}

void CGameComponent::clear()
{

	reset();
}

void CGameComponent::reset()
{
}

//**********************************定时器***************************************\\

bool CGameComponent::set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat /* = 1 */)
{

	bool ret = m_table->set_timer(time_id, timeout_msec, param, repeat);
	if (!ret)
	{
		MY_LOG_ERROR("set timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

bool CGameComponent::kill_timer(_uint8 time_id)
{

	bool ret = m_table->kill_timer(time_id);
	if (!ret)
	{
		MY_LOG_ERROR("kill timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

void CGameComponent::on_time(_uint8 time_id, _uint64 param)
{
	time_t current = time(NULL);
	int kicked_total_count = 0;

	if (time_id == TID_CHECK_USER)
	{
		for (auto &info : m_map_user_game_info)
		{
			if (current - info.second._last_kick_user_offline_tm_sec > KICK_USER_OFFLINE_DURATION)
			{
				IUser *user = m_table->get_user_from_uid(info.first);
				if (current - user->get_enter_time() > KICK_USER_OFFLINE_DURATION)
				{
					m_table->on_user_leave(user);
					m_map_user_game_info.erase(info.first);
					kicked_total_count++;
				}
			}
		}
	}
}

//**********************************客户端消息处理***************************************\\

// 接收客户端消息;
int CGameComponent::on_recv(_uint8 mcmd, _uint8 scmd, const void *pData, int size, IUser *pUser)
{

	return 0;
}

void CGameComponent::on_http_recv(socket_id sid, int uid, int opid, const std::string &str_param)
{
	MY_LOG_DEBUG("on_http_recv");
	IUser *user = m_table->get_user_from_uid(uid);
	if (user == nullptr)
	{
		string token = myredis::GetInstance()->get_data_by_uid_for_string(uid, DB_COMM, "current_token");
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::internal, token, "invalid user", nullptr, 0);
		MY_LOG_ERROR("user[%d] sid[%d] invalid user", uid, sid);
		return;
	}
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		user_game_info game_info;
		game_info.uid = uid;
		game_info.token = myredis::GetInstance()->get_data_by_uid_for_string(uid, DB_COMM, "current_token");
		game_info.user_type = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_USER_TYPE);
		game_info.web_token = myredis::GetInstance()->get_token_by_uid(uid);
		game_info.sub_client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_SUB_CHANNEL_ID);
		game_info.client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
		m_map_user_game_info[uid] = game_info;
		iter = m_map_user_game_info.find(uid);
	}
	iter->second._last_kick_user_offline_tm_sec = time(NULL);

	switch (opid)
	{
	case serverProto::AckType::spin:
	{
		_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
		user->set_gold(gold);
		on_user_bet_roll(user, str_param, iter->second);
	}
	break;

	case serverProto::AckType::info:
	{
		on_user_game_info(user, str_param, str_param.length(), iter->second);
	}
	break;

	case serverProto::AckType::jilijpSetting:
	{
		send_setting_result(user);
	}
	break;

	case serverProto::AckType::fullJpInfoAll:
	{
	}
	break;
	case serverProto::AckType::notice:
	{
		send_notice_result(user);
	}
	break;

	default:
		break;
	}
}

//**********************************房间消息处理***************************************\\

void CGameComponent::on_user_game_info(IUser *user, const std::string &data, int size, user_game_info &game_info)
{
	auto uid = user->get_user_id();
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", uid);
		return;
	}

	// todo : add error process
	serverProto::InfoReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb spin request", uid);
		return;
	}

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(req, &debugstr);
	MY_LOG_DEBUG("user[%d] on_user_game_info recved spin request: %s", uid, debugstr.c_str());

	// 单一钱包请求第三方刷新金币
	int api_type = user->get_api_type();
	if (api_type == MODE_SINGLE)
	{
		m_table->get_flush_user_gold(uid, GameConfig::GetInstance()->get_game_id());
		return;
	}
	_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	user->set_gold(gold);
	send_game_info_result(user, game_info.client_id);
}

void CGameComponent::handle_user_notice_request(IUser *user, const std::string &msg, int size)
{

	serverProto::NoticeReq req;
	if (req.ParseFromString(msg))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb notice request", user->get_user_id());
		return;
	}
}

// 房间消息处理;
int CGameComponent::on_user_action(IUser *user, _uint8 type)
{
	if (!user)
		return -1;

	_uint32 nUserID = user->get_user_id();
	switch (type)
	{
	case comm_action_sit_down:
	case comm_action_online:
	{
		on_user_enter(user);
	}
	break;
	case comm_action_leave:
	case comm_action_offline:
	{
		on_user_leave(user);
	}
	break;
	case comm_action_update_gold:
	{
		on_update_user_gold_result(user);
	}
	break;
	}
	return 0;
}

void CGameComponent::db_get_data(void *obj, const char *data, int len)
{

	db_data *post_data = (db_data *)obj;
	if (post_data == NULL)
		return;
}

/*
 * 判断玩家下线是否能删除玩家（用于加金币等玩家结算判断）
 */
bool CGameComponent::is_can_clear_user(int chairid)
{

	return false;
}

/*
 * 进入房间
 */
void CGameComponent::on_user_enter(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_enter user_id(%u)", pUser->get_user_id());
	m_table->send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_ROOM_SUCC, NULL);

	send_my_user_info(pUser);
	send_room_info(pUser);
}

// 离开房间
void CGameComponent::on_user_leave(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_leave user_id(%u)", pUser->get_user_id());
}

// 获取用户免费数据
int CGameComponent::get_user_free_data(int uid, const char str[32])
{
	char str_key[32] = {0};
	int game_id = GET_INT_CONFIG("game_id");
	sprintf(str_key, str, game_id, uid);
	int free_times = myredis::GetInstance()->get_user_temp_data(str_key);
	return free_times;
}

// 更新用户免费数据
void CGameComponent::update_user_free_data(int uid, const char str[32], int value)
{

	char str_key[32] = {0};
	int game_id = GET_INT_CONFIG("game_id");
	sprintf(str_key, str, game_id, uid);
	myredis::GetInstance()->update_user_temp_data(str_key, value);
}

// 设置用户免费数据
void CGameComponent::set_user_free_data(int uid, const char str[32], int value)
{

	char str_key[32] = {0};
	int game_id = GET_INT_CONFIG("game_id");
	sprintf(str_key, str, game_id, uid);
	myredis::GetInstance()->set_user_temp_data(str_key, value);
}

int get_time_mic()
{
	auto now = std::chrono::system_clock::now(); // 获取当前系统时钟时间点
	auto duration = now.time_since_epoch();		 // 计算从1970年到现在经过的时长

	long microseconds = std::chrono::duration_cast<std::chrono::microseconds>(duration).count(); // 将时长转换为微秒

	return microseconds%100000000LL;
}

// 请求下注
void CGameComponent::on_user_bet_roll(IUser *user, const std::string &data, user_game_info &game_info)
{
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", user->get_user_id());
		return;
	}

	// todo : add error process
	serverProto::SpinReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("failed to parse pb spin request, uid:%d", user->get_user_id());
		return;
	}

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(req, &debugstr);
	MY_LOG_DEBUG("user[%d] on_user_bet_roll recved spin request: %s", user->get_user_id(), debugstr.c_str());

	int bet_count = req.bet() * 100;
	
	int uid = user->get_user_id();
	game_info.reset();
	game_info.bet = bet_count;
	game_info.client_id = user->get_client_id();

	if (user->get_single_status() == 1)
	{
		MY_LOG_WARN("on_user_bet_roll status erro uid:%d", uid);
		return;
	}

	//std::string md5key;
	int now_time = get_time_mic();
	//calcu_md5key(uid, md5key, now_time);

	game_info.round_index = m_game_logic.gen_new_round_index();
	game_info.round_index_v2 = game_info.round_index * 1000 + 45;
	game_info.balance_before_round = user->get_gold();

	// 是否异步单一钱包模式，扣下注的钱
	if (user->get_api_type() == MODE_SINGLE)
	{
		write_transfe_inorut_one(-bet_count,
								 0,
								 GOLD_HANDRED_BET,
								 false,
								 bet_count,
								 uid,
								 false,
								 now_time
								 );

		return;
	}
	else
	{
		// 玩家是否金币不足
		_tint64 nUserGold = user->get_gold();
		if (bet_count > nUserGold)
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, game_info.token, "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet_count: %.3f, user gold: %lld", uid, bet_count, nUserGold);
			return;
		}
		user_bet_logic(user, game_info, false, bet_count, now_time);
	}
}

void CGameComponent::user_bet_logic(IUser *pUser, user_game_info &game_info, bool use_free, int total_bet, int now_time)
{
	int uid = pUser->get_user_id();

	if (!use_free)
	{
		m_table->update_gold_and_clore_single(uid, -total_bet, GOLD_HANDRED_BET, 0);
	}

	calcu_graph(game_info);
	calcu_result(game_info);

	if (game_info.result > 0)
	{
		m_table->update_gold_and_clore_single(uid, game_info.result, GOLD_DEL_GAME, 0);
	}
	//calcu_tax(pUser, md5key);
	write_transfe_inorut_one(game_info.result,
							 game_info.pay_out,
							 GOLD_DEL_GAME,
							 true,
							 game_info.bet,
							 uid,
							 true,
							 now_time
							 );

	write_versus_bill(uid, now_time);

	int bet = game_info.bet;
    // SpinRoundHistory h(game_info.uid, game_info.round_index, GameConfig::GetInstance()->get_game_id(), std::round(bet/100.0), 
    //         std::round(game_info.result/100.0) - std::round(bet/100.0), \
    //         ((float)game_info.balance_before_round + game_info.result - bet) / 100.0, 0, "PHP", game_info.record_id);
    // if (m_table) {
    //     m_table->write_spin_round_history(h);
    // }

	send_bet_roll_result(pUser, get_error_msg(MSG_ROOM_BET_SUCCESS, pUser->get_user_country()), true);
}

// 计算图形
void CGameComponent::calcu_graph(user_game_info &game_info, int i)
{
	game_info.result = 0;
	bool bRespin = false;
	string graph;
	int index = 0;
	bool bsucc = false;
	bool bRet = get_lottery_result(game_info.client_id, game_info.result);
	game_info.result /= 100;//redis里的数据是扩大100倍的
	MY_LOG_DEBUG("uid:%d get control result:%d", game_info.uid, game_info.result);
	if (game_info.result != 0)
	{  
		char szKey[64] = { 0 };
		sprintf(szKey, "CBT_%d", game_info.result);
		
		string strRedisKey = szKey;
		bsucc = myredis::GetInstance()->get_graph_from_redis(strRedisKey, graph, index, 3);
		MY_LOG_DEBUG("calcu_graph uid:%d get redis key:%s data:%s bsucc:%d", game_info.uid, strRedisKey.c_str(), graph.c_str(), bsucc);
		
		if (bsucc) //如果读取redis成功
		{
			bool bparse = m_game_logic.get_game_data_from_graph_data(game_info, graph);
            if (!bparse) {
				MY_LOG_WARN("calcu_graph uid:%d :%s json parse erro data:%s", game_info.uid, graph.c_str());
				bsucc = false;
		    }
		} else {
			MY_LOG_WARN("calcu_graph get redis failed uid:%d key:%s erro", game_info.uid, strRedisKey.c_str());
		}
	}
	if (!bsucc)
	{
		game_logic::GetInstance()->RandFillIconNoWin(game_info);
	}
	game_logic::GetInstance()->CalcResultTimes(game_info);

	// MY_LOG_DEBUG("calcu_graph uid:%d Icons:%d,%d,%d",
	// 	game_info.uid, game_info.Icons[0], game_info.Icons[1],game_info.Icons[2]);

	// MY_LOG_DEBUG("calcu_graph uid:%d plate_win:%d award_type:%d ",
	// 	game_info.uid, game_info.plate_win, game_info.award_type);

}

// 计算游戏结果
void CGameComponent::calcu_result(user_game_info &game_info)
{
	game_info.result = game_info.result;
	game_info.pay_out = game_info.result - game_info.bet;

	MY_LOG_DEBUG("calcu_result uid:%d result:%d pay_out:%d",
		game_info.uid, game_info.result, game_info.pay_out);
}

// 计算税收
void CGameComponent::calcu_tax(IUser *pUser, const std::string &md5key)
{
	int uid = pUser->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	// 输钱不用税收
	if (iter->second.pay_out <= 0)
		return;

	int tax = iter->second.pay_out * m_tax / 1000;
	if (tax == 0)
		return;

	MY_LOG_DEBUG("calcu_tax uid:%u tax:%d m_tax:%d",
				 uid, tax, m_tax);
	iter->second.tax = tax;
}

// 获得md5key值
void CGameComponent::calcu_md5key(int uid, string &strkey, int time)
{
	char md5str[36];
	char md5Key[33];
	memset(md5str, 0, sizeof(md5str));
	memset(md5Key, 0, sizeof(md5Key));
	mysprintf(md5str, sizeof(md5str), "%d%d%d%d", time,
			  m_table->get_room_id(), (int)m_table->get_node_id(), uid);
	g_com->_md5security(md5str, md5Key);
	strkey = md5Key;
}

/****************************************************/

// 打包玩家个人对局明细
void CGameComponent::pack_user_detail(string &detail, int uid)
{
	// todo: jili的游戏需要重新实现
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	Json::Value json_req_value;
	int bet = iter->second.bet;

	json_req_value["result"] = Json::Value(iter->second.result);
	json_req_value["bet"] = Json::Value(bet);
	json_req_value["payout"] = Json::Value(iter->second.pay_out);
	Json::FastWriter json_writer;
	detail = json_writer.write(json_req_value);
	detail = replace_all(detail, "\"", "\\\'", 0);
}

// 对局流水
void CGameComponent::write_versus_bill(int uid, int time)
{
	char szlist[10] = {0};
	Json::Value va;
	Json::Value json_req_value;
    int now = std::time(0);
	// 头部信息
	json_req_value["r_id"] = Json::Value("");
	json_req_value["post_time"] = Json::Value(now);
	json_req_value["room_id"] = Json::Value(m_table->get_room_id());
	json_req_value["table_id"] = Json::Value(m_table->get_node_id());
	json_req_value["players"] = Json::Value(1);
	json_req_value["tax"] = Json::Value(0);
	json_req_value["play_time"] = Json::Value(0); /* 本局耗时 */
	json_req_value["match_id"] = Json::Value(m_table->get_room_id());
	json_req_value["game_detail"] = Json::Value("");
	json_req_value["game_id"] = Json::Value(GameConfig::GetInstance()->get_game_id());
	json_req_value["node_id"] = Json::Value(m_table->get_node_id());
	json_req_value["room_mode"] = Json::Value(TYPE_HUNDRED);

	// 一批批用户数据打包写入
	iter_user_game_info itr = m_map_user_game_info.find(uid);
	if (itr != m_map_user_game_info.end())
	{
		IUser *user = m_table->get_user_from_uid(itr->first);
		va["uid"] = Json::Value(uid);
		int gold = 0;
		if (user)
		{
			va["nickname"] = Json::Value(user->get_nick_name());
			va["client_id"] = Json::Value(user->get_client_id());
			va["version"] = Json::Value(user->get_version());
			va["usertype"] = Json::Value(user->get_user_type());

			gold = user->get_gold();
		}
		else
		{
			va["client_id"] = Json::Value(0);
			va["version"] = Json::Value("");
			va["usertype"] = Json::Value(0);
			va["nickname"] = Json::Value("");
		}

		if (itr->second.pay_out == 0)
		{
			va["result"] = Json::Value(3); /* 流局|平分 */
		}
		else
		{
			if (itr->second.pay_out < 0)
				va["result"] = Json::Value(1); /* 输 */
			else
				va["result"] = Json::Value(2); /* 赢 */
		}

		va["times"] = Json::Value(0);					 /* 倍数 */
		va["score1"] = Json::Value(itr->second.pay_out); /* 当局变化的积分 */
		va["score2"] = Json::Value(gold);				 /* 当前积分 */
		va["point1"] = Json::Value(0);
		va["point2"] = Json::Value(0);

		string user_detail;
		pack_user_detail(user_detail, uid);
		va["poker_detail"] = Json::Value(user_detail.c_str());
		va["tax"] = Json::Value(0);
		int bet = itr->second.bet;

		va["bet"] = Json::Value(bet);

		mysprintf(szlist, sizeof(szlist), "player_%d", 0);
		json_req_value[szlist] = Json::Value(va);
	}
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	m_table->write_versus_bill(json_req_data.c_str(), json_req_data.length());
	MY_LOG_DEBUG("versus_bill: %s", json_req_data.c_str());
}

// 投付记录
void CGameComponent::write_transfe_inorut_one(int out_score,
											  int pay_out,
											  int bill_type,
											  bool is_end,
											  int bet,
											  int uid,
											  bool write_db,
											  int time
											  )
{
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	IUser *user = m_table->get_user_from_uid(uid);
	if (iter == m_map_user_game_info.end() || user == NULL)
	{
		MY_LOG_ERROR("write_transfe_inorut_one error");
		return;
	}
	hundred_transfer_inout_one data;
	char parent_id[64] = "";
	sprintf(parent_id, "%d%d%d", GET_INT_CONFIG("game_id"),uid,time);
	data.parent_id = parent_id;
	data.bill_type = bill_type;
	data.is_end = is_end;
	data.bet = bet;
	data.result = out_score;
	data.pay_out = pay_out;
	data.uid = uid;
	data.time = time;
	data.call_back = (bill_type == GOLD_HANDRED_BET);
	data.write_db = (bill_type == GOLD_DEL_GAME || bill_type == GOLD_DEL_TAX_REVENUE);
	data.api_type = user->get_api_type();
	data.sub_client_id = iter->second.sub_client_id;
	data.client_id = iter->second.client_id;
	data.web_token = iter->second.web_token;
	data.user_type = iter->second.user_type;
	m_table->write_transfer_inout(data);
}

bool CGameComponent::transfer_inout_result(transfer_inout_result_info data)
{
	MY_LOG_DEBUG("user[%d] transfer_inout_result req_info: %s is_success: %d erro_code: %d",
				 data.uid, data.req_info.c_str(),
				 data.is_suc ? 1 : 0,
				 data.erro_code);

	IUser *user = m_table->get_user_from_uid(data.uid);
	if (user == NULL)
	{
		MY_LOG_ERROR("user[%d] transfer_inout_result cannot found user by uid", data.uid);
		return false;
	}

	iter_user_game_info iter = m_map_user_game_info.find(data.uid);
	if (iter == m_map_user_game_info.end())
	{
		string token = myredis::GetInstance()->get_data_by_uid_for_string(data.uid, DB_COMM, "current_token");
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, token, "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return false;
	}

	int user_country = myredis::GetInstance()->get_data_by_uid(data.uid, DB_COMM, CUR_COUNTRY_CODE);
	if (!data.is_suc)
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, iter->second.token, "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return true;
	}

	if (data.erro_code != SUCCESS)
	{

		switch (data.erro_code)
		{
		case NOT_ENOUGH_BALANCE:
		{

			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, iter->second.token, "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet: %d", data.uid, data.bet);
			return true;
		}
		break;

		default:
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinCoinError, iter->second.token, "transfer gold failed", nullptr, 0);
			MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
			return true;
		}
		break;
		}
	}

	// 扣注成功后才才开始游戏逻辑
	user_bet_logic(user, iter->second, false, data.bet, data.time);

	return true;
}

void CGameComponent::on_update_user_gold_result(IUser *user)
{
	_uint32 uid = user->get_user_id();
	_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	user->set_gold(gold);
	MY_LOG_PRINT("user[%d] send game info response with gold: %d", uid, gold);
	int client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
	send_game_info_result(user, client_id);
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter != this->m_map_user_game_info.end())
	{
		iter->second.status = 1;
	}
}

//获得抽奖结果
bool CGameComponent::get_lottery_result(int client_id, int &result)
{ 
	int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();

	return CBT::winlose_control::GetInstance()->get_lottery_ctr_result(
		client_id,
		game_id,
		nodeid,
		result);
}

void CGameComponent::run_rtp()
{
	std::remove("data.txt");
	std::ofstream ofs;
	ofs.open("data.txt", ios::out | ios::app);
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 0;
	info.client_id = 31;

	int total = 0;
	int winCount = 0;
	int lianzhong = 0;
	int lianbuzhong = 0;
	int maxlianzhong = 0;
	int maxlianbuzhong = 0;
	int multer1 = 0;
	int multer2 = 0;
	int multer3 = 0;
	int multer4 = 0;
	int multer5 = 0;
	int multer6 = 0;
	int multer7 = 0;
	int multer8 = 0;
	int multer9 = 0;
	int test_count = 100000;
	int free = 0;

	for (int i = 0; i < test_count; i++)
	{
		MY_LOG_PRINT("The %d time", i + 1);
		calcu_graph(info);
		calcu_result(info);
		// MY_LOG_PRINT("cur result = %d", info.result);
		total += info.result;
		if (info.result > 0)
		{
			lianzhong++;
			winCount++;
			lianbuzhong = 0;
			if (lianzhong > maxlianzhong)
				maxlianzhong = lianzhong;
		}
		else
		{
			lianzhong = 0;
			lianbuzhong++;
			if (lianbuzhong > maxlianbuzhong)
				maxlianbuzhong = lianbuzhong;
		}

		int multer = info.result;
		if (multer <= 0)
			multer9++;
		else if (multer <= 100)
			multer1++;
		else if (multer > 100 && multer <= 300)
			multer2++;
		else if (multer > 300 && multer <= 500)
			multer3++;
		else if (multer > 500 && multer <= 1000)
			multer4++;
		else if (multer > 1000 && multer <= 2000)
			multer5++;
		else if (multer > 2000 && multer <= 5000)
			multer6++;
		else if (multer > 5000 && multer <= 10000)
			multer7++;
		else
		{
			multer8++;
		}
		if (info.free_count > 0)
		{
			free++;
		}
	}

	float rtp = float(total) / (test_count * 100);
	float winRate = float(winCount) / test_count;
	float freeRate = float(free) / test_count;
	ofs << "中奖概率: " << winRate * 100 << "%" << endl;
	ofs << "RTP: " << rtp * 100 << "%" << endl;
	ofs << "最大连中: " << maxlianzhong << endl;
	ofs << "freegame概率: " << freeRate * 100 << "%" << endl;
	// ofs << "最大连不中: " << maxlianbuzhong<< endl;

	rtp = float(multer9) / test_count;
	ofs << "0倍占比" << rtp * 100 << "%" << endl;
	rtp = float(multer1) / test_count;
	ofs << "0~1倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer2) / test_count;
	ofs << "1~3倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer3) / test_count;
	ofs << "3~5倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer4) / test_count;
	ofs << "5~10倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer5) / test_count;
	ofs << "10~20倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer6) / test_count;
	ofs << "20~50倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer7) / test_count;
	ofs << "50~100倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer8) / test_count;
	ofs << "100以上倍占比: " << rtp * 100 << "%" << endl;

	ofs.close();
}