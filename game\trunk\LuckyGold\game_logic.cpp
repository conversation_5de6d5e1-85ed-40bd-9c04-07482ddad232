#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include <cstdlib>
#include <ctime>
#include "nlohmann/json.hpp"
#include <algorithm>
using json = nlohmann::json;

// 符号枚举定义 (与game_logic.h中的enumIcon对应)
enum {
    Wild = 0,
    Bonus = 1,
    Symbol1 = 2,
    Symbol2 = 3,
    Symbol3 = 4,
    Symbol4 = 5,
    Symbol5 = 6,
    Symbol6 = 7,
    Mix7 = 8,
    MixBar = 9
};

// 30条连线的定义 (每条线包含3个位置的行索引)
static const vector<vector<int>> WIN_LINES = {
    // 第1-5条线：水平线
    {1, 1, 1}, {0, 0, 0}, {2, 2, 2}, {0, 1, 2}, {2, 1, 0},
    // 第6-10条线：V型和倒V型
    {1, 0, 1}, {1, 2, 1}, {0, 1, 0}, {2, 1, 2}, {1, 0, 2},
    // 第11-15条线
    {1, 2, 0}, {0, 0, 1}, {2, 2, 1}, {0, 0, 2}, {2, 2, 0},
    // 第16-20条线
    {1, 0, 0}, {1, 2, 2}, {0, 1, 1}, {2, 1, 1}, {0, 2, 2},
    // 第21-25条线
    {2, 0, 0}, {0, 2, 1}, {2, 0, 1}, {1, 1, 0}, {1, 1, 2},
    // 第26-30条线
    {0, 2, 0}, {2, 0, 2}, {0, 1, 2}, {2, 1, 0}, {1, 0, 1}
};

// 符号赔率表 (根据连续符号数量: 3个, 4个, 5个符号的赔率)
static const int SYMBOL_ODDS[10][4] = {
    // Wild, Bonus, Symbol1, Symbol2, Symbol3, Symbol4, Symbol5, Symbol6, Mix7, MixBar
    {0, 0, 0, 0},        // Wild (0) - 不单独计算赔率，作为百搭
    {0, 0, 0, 0},        // Bonus (1) - 不单独计算赔率，触发特色游戏
    {5, 25, 100, 500},   // Symbol1 (2) - 黄金7
    {3, 15, 50, 250},    // Symbol2 (3) - 银色7
    {3, 10, 30, 150},    // Symbol3 (4) - BAR*3
    {2, 8, 25, 100},     // Symbol4 (5) - BAR*2
    {2, 5, 20, 75},      // Symbol5 (6) - BAR
    {0, 0, 0, 0},        // Symbol6 (7) - 空白，不计算赔率
    {5, 25, 100, 500},   // Mix7 (8) - wild2X
    {0, 0, 0, 0}         // MixBar (9) - 深色bonus
};


game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::getMul(int count, int num) {
	int nMul = 1;
	for (int i = 0; i < count; i++) {
		nMul *= num;
	}
	return nMul;
}

void game_logic::CalcResultTimes(user_game_info &game_info)
{	
	game_info.result = 0;
	game_info.bonus = 0;
	int count = game_info.graph_data.size()/9;
	game_info.free_count = count - 1;
	game_info.free_count = game_info.free_count < 0 ? 0 : game_info.free_count;
	for (int num = 0; num < count; num++)
	{
		int icons[5][5] = {0};
		for (int i = 0; i < 5; i++)
		{
			for (int j = 0; j < 5; j++) {
				icons[i][j] = game_info.graph_data[num*9 + i*3 + j];
			}
		}
		CalcResultTimes(game_info, icons, num);
	}
	game_info.result *= 100;
}

int game_logic::CalcResultTimes(user_game_info &gameInfo, int icons[5][5], int pageNum)
{
	/**游戏规则说明
	1.一般游戏转出5个以上红利金砖可开启点石成金特色游戏。
	2.在点石成金中，依照在一般游戏中转出的BONUS金砖个数，有多次机会随机选中带分金砖。 对应 BonusCount的定义
	3.所有被选中的带分金砖加总后，成为免费游戏里的金砖价值。并开启 7 局免费游戏。
	4.在免费游戏中，转出任一个BONUS金砖，立即获得金砖价值的赢分。
	5.免费游戏中转出5个以上BONUS金砖，可再重复触发点石成金。继续叠加金砖价值。
	6.重复触发点石成金后，再增加 5 局免费游戏

	7.赔付线数为30线。
	8.所有获奖图案，必须由最左边转轮开始，由左至右连续出现。
	9.仅支付每条在线所中奖的最高金额。
	10.获胜组合及其奖金将依照赔付表进行发放 对应的数据是Symbol1_odd Symbol2_odd这些

	JP彩金
	在点石成金中，有机会转中JP金砖。则直接获得JP彩金。
	获得JP彩金，点石成金特色游戏结束，回到一般游戏。
	*/

	bool mainGame = pageNum == 0;
	if (mainGame) {
		gameInfo.result = 0;
		gameInfo.plate_win = 0;
		gameInfo.normal_game_win = 0;
		gameInfo.free_game_win = 0;
		gameInfo.free_game_bonus_win = 0;
	}

	// 1. 检查所有连线中奖情况
	CheckAllWinLines(gameInfo, icons);

	// 2. 检查BONUS特色游戏触发
	int bonus_count = 0;
	bool bonus_triggered = CheckBonusFeature(icons, bonus_count);

	if (gameInfo.game_state == NORMAL_GAME) {
		// 一般游戏逻辑
		if (bonus_triggered) {
			gameInfo.bonus_count = bonus_count;
			gameInfo.total_bonus_count += bonus_count;
			gameInfo.game_state = BONUS_SELECTION;
			ProcessBonusSelection(gameInfo);
		}
	} else if (gameInfo.game_state == FREE_GAME) {
		// 免费游戏逻辑
		ProcessFreeGame(gameInfo, icons);
	}

	// 3. 更新连线标记
	UpdateConnectMap(gameInfo);

	// 4. 计算总奖励
	int roundResult = gameInfo.plate_win;
	gameInfo.result += roundResult;

	return gameInfo.result;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::RandFillIconNoWin(user_game_info &game_info)
{  
	int Icon[5][5] = {0};
	int nBonus1 = 0;
	

	game_info.graph_data.clear();
	for (int i = 0; i < 5; i++)
	{
		for (int j = 0; j < 5; j++)
		{
			game_info.graph_data.push_back(Icon[i][j]);
		}
	}
}

bool game_logic::get_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
	game_info.reset();
	for (auto &d : data)
	{
		for (auto &g : d)
		{
			game_info.graph_data.push_back(g);
		}
	}
	MY_LOG_PRINT("graph len = %d", game_info.graph_data.size());
	// MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}

// 通用中奖判断方法 - 检查指定连线是否中奖
bool game_logic::CheckWinLine(const int icons[3][3], const vector<int>& line_pattern, int& win_symbol, int& win_count)
{
    if (line_pattern.size() != 3) return false;

    // 获取连线上的符号
    int symbols[3];
    for (int i = 0; i < 3; i++) {
        symbols[i] = icons[line_pattern[i]][i];
    }

    // 检查从左到右的连续符号
    win_symbol = symbols[0];
    win_count = 1;

    // 如果第一个符号是Wild，找到第一个非Wild符号作为基准
    if (win_symbol == Wild) {
        for (int i = 1; i < 3; i++) {
            if (symbols[i] != Wild && symbols[i] != Bonus) {
                win_symbol = symbols[i];
                break;
            }
        }
    }

    // 如果基准符号是Bonus，不计算连线奖励
    if (win_symbol == Bonus) return false;

    // 计算连续符号数量
    for (int i = 1; i < 3; i++) {
        if (symbols[i] == win_symbol || symbols[i] == Wild) {
            win_count++;
        } else {
            break;
        }
    }

    // 至少需要3个连续符号才中奖
    return win_count >= 3;
}

// 检查所有30条连线
void game_logic::CheckAllWinLines(user_game_info &game_info, const int icons[3][3])
{
    game_info.win_lines.clear();
    game_info.connect_map.assign(5, vector<bool>(5, false));

    int total_win = 0;

    for (int line_id = 0; line_id < WIN_LINES.size() && line_id < 30; line_id++) {
        int win_symbol, win_count;
        if (CheckWinLine(icons, WIN_LINES[line_id], win_symbol, win_count)) {
            WinLineInfo win_line;
            win_line.line_id = line_id;
            win_line.symbol = win_symbol;
            win_line.count = win_count;

            // 计算中奖金额
            int odds = GetSymbolOdds(win_symbol, win_count);
            win_line.win_amount = (game_info.bet / 100) * odds;
            total_win += win_line.win_amount;

            // 记录中奖位置
            for (int i = 0; i < win_count; i++) {
                win_line.positions.push_back(i);
                // 标记连线位置 (扩展到5x5网格)
                int row = WIN_LINES[line_id][i];
                int col = i + 1; // 中间3列
                game_info.connect_map[col][row] = true;
            }

            game_info.win_lines.push_back(win_line);
        }
    }

    // 根据游戏状态更新相应的奖金
    if (game_info.game_state == NORMAL_GAME) {
        game_info.normal_game_win = total_win;
        game_info.plate_win = total_win;
    } else if (game_info.game_state == FREE_GAME) {
        game_info.free_game_win = total_win;
        game_info.plate_win = total_win;
    }
}

// 计算符号赔率
int game_logic::GetSymbolOdds(int symbol, int count)
{
    if (symbol < 0 || symbol >= 10 || count < 3 || count > 5) {
        return 0;
    }

    // count: 3=index0, 4=index1, 5=index2
    int odds_index = count - 3;
    if (odds_index >= 0 && odds_index < 4) {
        return SYMBOL_ODDS[symbol][odds_index];
    }

    return 0;
}

// 检查是否触发BONUS特色游戏
bool game_logic::CheckBonusFeature(const int icons[3][3], int& bonus_count)
{
    bonus_count = 0;

    // 统计所有BONUS符号数量
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            if (icons[i][j] == Bonus) {
                bonus_count++;
            }
        }
    }

    // 5个以上BONUS触发点石成金
    return bonus_count >= 5;
}

// 处理点石成金特色游戏
void game_logic::ProcessBonusSelection(user_game_info &game_info)
{
    // 根据BONUS数量确定选择次数
    int selection_count = game_info.bonus_count;

    // 模拟选择带分金砖的过程
    double selected_gold_value = 0.0;
    for (int i = 0; i < selection_count; i++) {
        // 随机选择金砖价值 (这里使用简单的随机值，实际应该根据配置)
        double gold_value = (rand() % 100 + 10) * (game_info.bet / 100.0);
        selected_gold_value += gold_value;
    }

    game_info.gold_value += selected_gold_value;
    game_info.remaining_free_spins = 7; // 开启7局免费游戏
    game_info.game_state = FREE_GAME;
}

// 处理免费游戏
void game_logic::ProcessFreeGame(user_game_info &game_info, const int icons[3][3])
{
    // 检查免费游戏中的BONUS
    int bonus_count = 0;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            if (icons[i][j] == Bonus) {
                bonus_count++;
            }
        }
    }

    // 每个BONUS获得金砖价值的赢分
    if (bonus_count > 0) {
        game_info.free_game_bonus_win = bonus_count * game_info.gold_value;
        game_info.plate_win += game_info.free_game_bonus_win;
    }

    // 检查是否再次触发点石成金
    if (bonus_count >= 5) {
        game_info.bonus_count = bonus_count;
        ProcessBonusSelection(game_info);
        game_info.remaining_free_spins += 5; // 再增加5局免费游戏
    }

    game_info.remaining_free_spins--;
    if (game_info.remaining_free_spins <= 0) {
        game_info.game_state = NORMAL_GAME;
    }
}

// 更新连线标记
void game_logic::UpdateConnectMap(user_game_info &game_info)
{
    // 连线标记已在CheckAllWinLines中更新
    // 这里可以添加额外的逻辑，比如BONUS符号的特殊标记

    // 如果是免费游戏，标记所有BONUS位置
    if (game_info.game_state == FREE_GAME) {
        // 从3x3数据中找到BONUS位置并标记到5x5网格
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                int symbol_index = i * 3 + j;
                if (symbol_index < game_info.graph_data.size() &&
                    game_info.graph_data[symbol_index] == Bonus) {
                    // 映射到5x5网格的中间3x3区域
                    game_info.connect_map[j + 1][i + 1] = true;
                }
            }
        }
    }
}
