#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include <cstdlib>
#include <ctime>
#include "nlohmann/json.hpp"
#include <algorithm>
using json = nlohmann::json;


game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::getMul(int count, int num) {
	int nMul = 1;
	for (int i = 0; i < count; i++) {
		nMul *= num;
	}
	return nMul;
}

void game_logic::CalcResultTimes(user_game_info &game_info)
{	
	game_info.result = 0;
	game_info.bonus = 0;
	int count = game_info.graph_data.size()/9;
	game_info.free_count = count - 1;
	game_info.free_count = game_info.free_count < 0 ? 0 : game_info.free_count;
	for (int num = 0; num < count; num++)
	{
		int icons[5][5] = {0};
		for (int i = 0; i < 5; i++)
		{
			for (int j = 0; j < 5; j++) {
				icons[i][j] = game_info.graph_data[num*9 + i*3 + j];
			}
		}
		CalcResultTimes(game_info, icons, num);
	}
	game_info.result *= 100;
}

int game_logic::CalcResultTimes(user_game_info &gameInfo, int icons[5][5], int pageNum)
{   
	/**TODO：以下是游戏规则说明
	1.一般游戏转出5个以上红利金砖可开启点石成金特色游戏。
	2.在点石成金中，依照在一般游戏中转出的BONUS金砖个数，有多次机会随机选中带分金砖。 对应 BonusCount的定义
	3.所有被选中的带分金砖加总后，成为免费游戏里的金砖价值。并开启 7 局免费游戏。
	4.在免费游戏中，转出任一个BONUS金砖，立即获得金砖价值的赢分。
	5.免费游戏中转出5个以上BONUS金砖，可再重复触发点石成金。继续叠加金砖价值。
	6.重复触发点石成金后，再增加 5 局免费游戏

	7.赔付线数为30线。
	8.所有获奖图案，必须由最左边转轮开始，由左至右连续出现。
	9.仅支付每条在线所中奖的最高金额。
	10.获胜组合及其奖金将依照赔付表进行发放 对应的数据是Symbol1_odd Symbol2_odd这些

	JP彩金
	在点石成金中，有机会转中JP金砖。则直接获得JP彩金。
	获得JP彩金，点石成金特色游戏结束，回到一般游戏。
	*/
	bool mainGame = pageNum == 0;
	if (mainGame) {
		gameInfo.result = 0;
	}

	gameInfo.award_type = -1;
	int bet = gameInfo.bet / 100;

	int odds = 0;

	int bonus = 0;
	int roundResult = 0;

	gameInfo.plate_win = roundResult;
	gameInfo.result += gameInfo.plate_win;

	return gameInfo.result;
	
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::RandFillIconNoWin(user_game_info &game_info)
{  
	int Icon[5][5] = {0};
	int nBonus1 = 0;
	

	game_info.graph_data.clear();
	for (int i = 0; i < 5; i++)
	{
		for (int j = 0; j < 5; j++)
		{
			game_info.graph_data.push_back(Icon[i][j]);
		}
	}
}

bool game_logic::get_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
	game_info.reset();
	for (auto &d : data)
	{
		for (auto &g : d)
		{
			game_info.graph_data.push_back(g);
		}
	}
	MY_LOG_PRINT("graph len = %d", game_info.graph_data.size());
	// MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}
