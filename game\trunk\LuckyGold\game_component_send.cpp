#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "server.pb.h"
#include "luckygoldbricks.pb.h"
#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "google/protobuf/message.h"
#include "google/protobuf/descriptor.h"

#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <fstream>
#include <cmath>
#include <array>
#include "winlose.h"
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/buffer.h>
// AES 密钥长度（256-bit = 32 字节）
constexpr size_t AES_KEY_SIZE = 32;
constexpr size_t AES_BLOCK_SIZE = 16;

const int GO_TO_REPLAY = 1;//是否进入回放

std::string base64Encode(const std::vector<unsigned char>& data) 
{
    BIO* bio, * b64;
    BUF_MEM* bufferPtr;
    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);
    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);  // 不加换行符
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    std::string encoded(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);
    return encoded;
}

// AES-CBC 加密函数
std::vector<unsigned char> encryptAESCBC(const std::string& plaintext, const std::string& token) 
{
    std::vector<unsigned char> iv(AES_BLOCK_SIZE);
    RAND_bytes(iv.data(), AES_BLOCK_SIZE);  // 生成随机 IV

    // 截取 token 前 32 字节作为 key
    std::string keyString = token.substr(0, AES_KEY_SIZE);
    std::vector<unsigned char> key(keyString.begin(), keyString.end());

    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) throw std::runtime_error("Failed to create context");

    if (1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key.data(), iv.data()))
        throw std::runtime_error("EncryptInit failed");

    std::vector<unsigned char> ciphertext(plaintext.size() + AES_BLOCK_SIZE);
    int len = 0;
    int ciphertext_len = 0;

    if (1 != EVP_EncryptUpdate(ctx, ciphertext.data(), &len,
                               reinterpret_cast<const unsigned char*>(plaintext.data()),
                               plaintext.size()))
        throw std::runtime_error("EncryptUpdate failed");

    ciphertext_len += len;

    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext.data() + ciphertext_len, &len))
        throw std::runtime_error("EncryptFinal failed");

    ciphertext_len += len;
    ciphertext.resize(ciphertext_len);  // 截取实际密文长度

    EVP_CIPHER_CTX_free(ctx);

    // 合并 IV + 密文
    std::vector<unsigned char> result;
    result.insert(result.end(), iv.begin(), iv.end());
    result.insert(result.end(), ciphertext.begin(), ciphertext.end());

    return result;
}

std::string base64_decode(const std::string &encoded_string)
{
	// Base64字符集
	static const std::string base64_chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		"abcdefghijklmnopqrstuvwxyz"
		"0123456789+/";

	auto is_base64 = [](unsigned char c) -> bool
	{
		return (isalnum(c) || (c == '+') || (c == '/'));
	};

	// 清除空格等非法字符
	std::string clean_input;
	for (auto c : encoded_string)
	{
		if (is_base64(c) || c == '=')
		{
			clean_input += c;
		}
	}

	int in_len = clean_input.size();
	int i = 0;
	int j = 0;
	int in_ = 0;
	unsigned char char_array_4[4], char_array_3[3];
	std::string ret;

	while (in_len-- && (clean_input[in_] != '=') && is_base64(clean_input[in_]))
	{
		char_array_4[i++] = clean_input[in_];
		in_++;
		if (i == 4)
		{
			for (i = 0; i < 4; i++)
			{
				char_array_4[i] = base64_chars.find(char_array_4[i]);
			}

			char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
			char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
			char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

			for (i = 0; i < 3; i++)
			{
				ret += char_array_3[i];
			}
			i = 0;
		}
	}

	if (i)
	{
		for (j = i; j < 4; j++)
		{
			char_array_4[j] = 0;
		}

		for (j = 0; j < 4; j++)
		{
			char_array_4[j] = base64_chars.find(char_array_4[j]);
		}

		char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
		char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
		char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

		for (j = 0; j < i - 1; j++)
		{
			ret += char_array_3[j];
		}
	}

	return ret;
}

bool compare_graph(const luckygoldProto::AllPlate &g1, const luckygoldProto::AllPlate &g2, std::string &diff)
{
	bool equal = true;

	google::protobuf::util::MessageDifferencer differencer;
	differencer.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
	// differencer.set_treat_nan_as_equal();

	const google::protobuf::FieldDescriptor *field = luckygoldProto::AllPlate::descriptor()->FindFieldByName("NextRow");
	if (field != nullptr)
	{
		differencer.IgnoreField(field);
	}

	std::string d = "";
	differencer.ReportDifferencesToString(&diff);
	if (!differencer.Compare(g1, g2))
	{
		equal = false;
		return false;
	}

	diff = "equal";
	return equal;
}

void CGameComponent::send_data(IUser *pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *pData)
{

	if (pUser)
		m_table->send(pUser, mcmd, scmd, pData);
	else
		m_table->send_user_batch(mcmd, scmd, pData);
}

void CGameComponent::send_bet_roll_err_result(IUser *user, serverProto::AckType type, serverProto::Error err,
											  const std::string &token, const std::string &msg, void *data, size_t size)
{
	serverProto::GaiaResponse response;
    response.set_type(type);
    response.set_ret(err);
    response.set_errormsg(msg);
    response.set_token(token);
    auto encrypted = encryptAESCBC("", token);
    response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());

    std::string content = response.SerializeAsString();

    svrlib::socket_id sid = user->get_socket_id();
    if (m_table)
    {
        m_table->send_http(sid, content);
    }
    else
    {
        MY_LOG_ERROR("user[%d] invalid table", user->get_user_id());
    }
}

void encodeACKProto(luckygoldProto::AllPlate &ack, user_game_info &game_info)
{
	// 清空现有数据
	ack.clear_plate();
	ack.clear_jp();

	// 创建一个 Plate 对象
	luckygoldProto::Plate* plate = ack.add_plate();

	// 从 graph_data 构建 5x5 的网格数据
	// 根据实际的游戏逻辑，可能是 3x3 或 5x5
	int grid_size = 5; // 默认 5x5
	int data_size = game_info.graph_data.size();

	// 判断是 3x3 还是 5x5 的数据
	if (data_size == 9) {
		grid_size = 3;
	} else if (data_size >= 25) {
		grid_size = 5;
	}

	if (data_size >= grid_size * grid_size) {
		// 创建 5 列 (对应 JSON 中的 column 数组)
		for (int col = 0; col < 5; col++) {
			luckygoldProto::ColumnData* column = plate->add_column();

			// 每列创建 5 行 (对应 JSON 中的 row 数组)
			for (int row = 0; row < 5; row++) {
				luckygoldProto::Cell* cell = column->add_row();

				// 如果是 3x3 数据，只填充中间的 3x3 区域
				if (grid_size == 3) {
					// 只在中间 3x3 区域填充数据
					if (col >= 1 && col <= 3 && row >= 1 && row <= 3) {
						int data_col = col - 1;
						int data_row = row - 1;
						int symbol_value = game_info.graph_data[data_row * 3 + data_col];

						// 设置符号类型
						luckygoldProto::Symbol symbol;
						switch (symbol_value) {
							case 0: symbol = luckygoldProto::Wild; break;
							case 1: symbol = luckygoldProto::Bonus; break;
							case 2: symbol = luckygoldProto::Symbol1; break;
							case 3: symbol = luckygoldProto::Symbol2; break;
							case 4: symbol = luckygoldProto::Symbol3; break;
							case 5: symbol = luckygoldProto::Symbol4; break;
							case 6: symbol = luckygoldProto::Symbol5; break;
							case 7: symbol = luckygoldProto::Symbol6; break;
							case 8: symbol = luckygoldProto::Mix7; break;
							case 9: symbol = luckygoldProto::MixBar; break;
							default: symbol = luckygoldProto::Wild; break;
						}
						cell->set_symbol(symbol);

						// 设置连接状态 (中间行的符号可能参与连线)
						if (data_row == 1) { // 中间行
							// 这里可以根据游戏逻辑判断是否连线
							// 暂时设为 false
							cell->set_isconnect(false);
						} else {
							cell->set_isconnect(false);
						}
					} else {
						// 边缘区域设为空或默认符号
						cell->set_symbol(luckygoldProto::Wild);
						cell->set_isconnect(false);
					}
				} else {
					// 5x5 数据，直接映射
					int symbol_value = game_info.graph_data[col * 5 + row];

					// 设置符号类型
					luckygoldProto::Symbol symbol;
					switch (symbol_value) {
						case 0: symbol = luckygoldProto::Wild; break;
						case 1: symbol = luckygoldProto::Bonus; break;
						case 2: symbol = luckygoldProto::Symbol1; break;
						case 3: symbol = luckygoldProto::Symbol2; break;
						case 4: symbol = luckygoldProto::Symbol3; break;
						case 5: symbol = luckygoldProto::Symbol4; break;
						case 6: symbol = luckygoldProto::Symbol5; break;
						case 7: symbol = luckygoldProto::Symbol6; break;
						case 8: symbol = luckygoldProto::Mix7; break;
						case 9: symbol = luckygoldProto::MixBar; break;
						default: symbol = luckygoldProto::Wild; break;
					}
					cell->set_symbol(symbol);

					// 设置连接状态 (这里需要根据游戏逻辑来判断)
					// 暂时设为 false，可以根据实际需求修改
					cell->set_isconnect(false);
				}
			}
		}
	}

	// 设置赢取金额
	plate->set_win(game_info.plate_win);

	// 设置金币信息
	int total_bonus_count = 0;
	for (int i = 0; i < 3; i++) {
		total_bonus_count += game_info.bonus_count[i];
	}

	if (total_bonus_count > 0) {
		luckygoldProto::BonusInfo* gold = plate->mutable_gold();
		gold->set_count(total_bonus_count);
		gold->set_barwin(0.0); // 设置 barWin

		// 可以根据需要添加金币数组
		// gold->add_gold(value);
	}

	// 设置 JP 信息 (奖池信息)
	luckygoldProto::JpCurrency* jp_currency = ack.add_jp();
	jp_currency->set_currency(1); // 设置货币类型，默认为1

	// 添加 JP 信息 (3个奖池等级)
	// 这里使用示例数据，实际应该从配置或数据库获取
	double jp_values[3] = {1096.27, 1753.27, 3518.90}; // 示例奖池值
	for (int i = 0; i < 3; i++) {
		luckygoldProto::JpInfo* jp_info = jp_currency->add_info();
		jp_info->set_id(i + 1);
		jp_info->set_win(jp_values[i]);
	}
}

void CGameComponent::send_bet_roll_result(IUser *user, std::string msg, bool is_suc)
{
	MY_LOG_DEBUG("send_bet_roll_result");	
	serverProto::SpinResponse spin;

	serverProto::ServiceData *service = spin.mutable_service();
	serverProto::freeSpinList *freeSpinList= service->mutable_freeremainv2();
	auto uid = user->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		MY_LOG_ERROR("user[%d] game info is empty", user->get_user_id());
		return;
	}

	auto gold = user->get_gold();
	spin.set_roundindexv2(iter->second.round_index_v2);
	spin.set_postmoney(std::round(gold) / 100.0);
	spin.set_totalwin(std::round(iter->second.result)/ 100.0);
	spin.set_hasspin(true);
	spin.set_basebet(std::round(iter->second.bet) / 100.0);
	spin.set_realbet(std::round(iter->second.bet) / 100.0);

	serverProto::SpinReq *req = spin.mutable_spinreq();
	req->set_bet(std::round(iter->second.bet) / 100.0);
	req->mutable_special();
	luckygoldProto::AllPlate ack;
	// encodeACKProto(ack, iter->second);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = false;
	// google::protobuf::util::MessageToJsonString(ack, &json_string, options);
	// MY_LOG_PRINT("ack = %s", json_string.c_str());

	luckygoldProto::AllPlate demo;

	if(GO_TO_REPLAY)
	{
		static int i = 0;
		// i = 590;
		MY_LOG_PRINT("第%d个图形", i + 1);
		string dd;
		myredis::GetInstance()->get_graph_from_redis_by_index("luckygold_reply", dd, i, 3);
		i++;
		if (i > 1000)
		{
			i = 0;
		}
		std::string decoded = base64_decode(dd);
		demo.ParseFromString(decoded);
		
		// //TODO 测试
		demo.mutable_plate(0).mutable_column(0)->mutable_row(0)->set_symbol(luckygoldProto::Symbol1);
		demo.mutable_plate(0).mutable_column(0)->mutable_row(1)->set_symbol(luckygoldProto::Symbol2);
		demo.mutable_plate(0).mutable_column(0)->mutable_row(2)->set_symbol(luckygoldProto::Symbol3);
		demo.mutable_plate(0).mutable_column(0)->mutable_row(3)->set_symbol(luckygoldProto::Symbol4);
		demo.mutable_plate(0).mutable_column(0)->mutable_row(4)->set_symbol(luckygoldProto::Symbol5);

		demo.mutable_plate(0).mutable_column(1)->mutable_row(0)->set_symbol(luckygoldProto::Symbol1);
		demo.mutable_plate(0).mutable_column(1)->mutable_row(1)->set_symbol(luckygoldProto::Symbol2);
		demo.mutable_plate(0).mutable_column(1)->mutable_row(2)->set_symbol(luckygoldProto::Symbol3);
		demo.mutable_plate(0).mutable_column(1)->mutable_row(3)->set_symbol(luckygoldProto::Symbol4);
		demo.mutable_plate(0).mutable_column(1)->mutable_row(4)->set_symbol(luckygoldProto::Symbol5);

		demo.mutable_plate(0).mutable_column(2)->mutable_row(0)->set_symbol(luckygoldProto::Symbol6);
		demo.mutable_plate(0).mutable_column(2)->mutable_row(1)->set_symbol(luckygoldProto::Bonus);
		demo.mutable_plate(0).mutable_column(2)->mutable_row(2)->set_symbol(luckygoldProto::Mix7);
		demo.mutable_plate(0).mutable_column(2)->mutable_row(3)->set_symbol(luckygoldProto::MixBar);
		demo.mutable_plate(0).mutable_column(2)->mutable_row(4)->set_symbol(luckygoldProto::Wild);

		demo.mutable_plate(0).mutable_column(3)->mutable_row(0)->set_symbol(luckygoldProto::Symbol6);
		demo.mutable_plate(0).mutable_column(3)->mutable_row(1)->set_symbol(luckygoldProto::Bonus);
		demo.mutable_plate(0).mutable_column(3)->mutable_row(2)->set_symbol(luckygoldProto::Mix7);
		demo.mutable_plate(0).mutable_column(3)->mutable_row(3)->set_symbol(luckygoldProto::MixBar);
		demo.mutable_plate(0).mutable_column(3)->mutable_row(4)->set_symbol(luckygoldProto::Wild);

		demo.mutable_plate(0).mutable_column(4)->mutable_row(0)->set_symbol(luckygoldProto::Symbol1);
		demo.mutable_plate(0).mutable_column(4)->mutable_row(1)->set_symbol(luckygoldProto::Symbol2);
		demo.mutable_plate(0).mutable_column(4)->mutable_row(2)->set_symbol(luckygoldProto::Symbol3);
		demo.mutable_plate(0).mutable_column(4)->mutable_row(3)->set_symbol(luckygoldProto::Symbol4);
		demo.mutable_plate(0).mutable_column(4)->mutable_row(4)->set_symbol(luckygoldProto::Symbol5);

		std::string debugstr;
		using namespace google::protobuf;
		TextFormat::PrintToString(demo, &debugstr);
		MY_LOG_DEBUG("send_bet_roll_result demo: %s", debugstr.c_str());
	}

	std::string *d = spin.mutable_data();
	if(GO_TO_REPLAY)
		demo.SerializeToString(d);
	else
		ack.SerializeToString(d);
	//
	json_string.clear();
	// google::protobuf::util::MessageToJsonString(spin, &json_string, options);
	// MY_LOG_PRINT("spin = %s", json_string.c_str());

	string data;
	spin.SerializeToString(&data);

	serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::spin);
	if(iter->second.token.length() >= AES_KEY_SIZE)
	{
		std::vector<unsigned char> encrypted = encryptAESCBC(data, iter->second.token);
		// string str = base64Encode(encrypted);
		// MY_LOG_PRINT("str = %s", str.c_str());

		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
		json_string.clear();

		response.set_token(iter->second.token);
		//google::protobuf::util::MessageToJsonString(response, &json_string, options);
		//MY_LOG_PRINT("response = %s", json_string.c_str());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content = "";
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
	MY_LOG_DEBUG("user[%d] send spin response ok with content length: %d total win: %.3f post_money: %.3f",
				 uid, content.length(), spin.totalwin(), spin.postmoney());
}

void CGameComponent::test_bet_roll_result(user_game_info &game_info, int i)
{
	luckygoldProto::AllPlate ack;
	encodeACKProto(ack, game_info);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = false;

	google::protobuf::util::MessageToJsonString(ack, &json_string, options);

	std::remove("test.txt");
	std::ofstream ofs;
	ofs.open("test.txt", ios::out | ios::app);
	ofs << json_string;

	string dd;
	myredis::GetInstance()->get_graph_from_redis_by_index("luckygold_reply", dd, i, 3);
	std::string decoded = base64_decode(dd);
	luckygoldProto::AllPlate demo;
	demo.ParseFromString(decoded);

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(demo, &debugstr);
	MY_LOG_DEBUG("test demo: %s", debugstr.c_str());

	string diff;
	if (!compare_graph(ack, demo, diff))
	{
		MY_LOG_PRINT("graph%d diff = %s", i + 1, diff.c_str());
	}
	else
	{
		MY_LOG_PRINT("graph%d equal", i + 1);
	}

	ofs.close();
}

void CGameComponent::send_room_info(IUser *pUser)
{
}

void CGameComponent::send_my_user_info(IUser *pUser)
{
}

void CGameComponent::send_game_info_result(IUser *user, int client_id)
{
	uint32_t uid = user->get_user_id();

    serverProto::GameInfoAck ack;
	serverProto::WalletInfo *wallet = ack.add_walletinfo();
	int64_t gold = user->get_gold();
	wallet->set_coin(std::round(gold) / 100.0);
    int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();
	int base_bet = CBT::winlose_control::GetInstance()->get_base_bet(client_id, game_id, nodeid);
	if (base_bet == 0)
	{
		base_bet = 1;
	}
    for (int32_t i = 0; i < MAX_BET_CONFIG_COUNT; i++)
	{
		wallet->add_bet(bet_config_count[i] * (double)base_bet);
	}
	wallet->set_currencynumber(33);
	//wallet->set_currencyname("PKR");
	//wallet->set_currencysymbol("Rs");
	wallet->set_unit(1);
	wallet->set_ratio(1);
	wallet->set_rate(0.64);
	wallet->set_decimal(4);

	ack.set_maxodd(1250);
	// ack.set_freespintype(-1);
	
	// luckygoldProto::GameInfoAck gameinfo;
	// for (const auto &value : odd_info)
	// {   
	// 	gameinfo.add_oddlist(value);
	// }
	// std::string *pExtrainInfo = ack.mutable_extrainfo();
	// gameinfo.SerializeToString(pExtrainInfo);

	serverProto::freeSpinList *freespin = ack.mutable_freespin();

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::info);	
	string data;
	ack.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}


void CGameComponent::send_notice_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::NoticeInfo info;
	
	int64_t gold = user->get_gold();
	info.set_type(0);
	info.set_value(0);
	info.set_currencynumber(33);

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::notice);	
	string data;
	info.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}

void CGameComponent::send_setting_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::JiliJpSetting setting;
	
    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::jilijpSetting);	
	string data;
	setting.SerializeToString(&data);
	MY_LOG_DEBUG("data = %s", data.c_str());

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}