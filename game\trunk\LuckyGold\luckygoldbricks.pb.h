// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: luckygoldbricks.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_luckygoldbricks_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_luckygoldbricks_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_luckygoldbricks_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_luckygoldbricks_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_luckygoldbricks_2eproto;
namespace luckygoldProto {
class AllPlate;
struct AllPlateDefaultTypeInternal;
extern AllPlateDefaultTypeInternal _AllPlate_default_instance_;
class Block;
struct BlockDefaultTypeInternal;
extern BlockDefaultTypeInternal _Block_default_instance_;
class BonusInfo;
struct BonusInfoDefaultTypeInternal;
extern BonusInfoDefaultTypeInternal _BonusInfo_default_instance_;
class Cell;
struct CellDefaultTypeInternal;
extern CellDefaultTypeInternal _Cell_default_instance_;
class ColumnData;
struct ColumnDataDefaultTypeInternal;
extern ColumnDataDefaultTypeInternal _ColumnData_default_instance_;
class JpCurrency;
struct JpCurrencyDefaultTypeInternal;
extern JpCurrencyDefaultTypeInternal _JpCurrency_default_instance_;
class JpInfo;
struct JpInfoDefaultTypeInternal;
extern JpInfoDefaultTypeInternal _JpInfo_default_instance_;
class Plate;
struct PlateDefaultTypeInternal;
extern PlateDefaultTypeInternal _Plate_default_instance_;
}  // namespace luckygoldProto
PROTOBUF_NAMESPACE_OPEN
template<> ::luckygoldProto::AllPlate* Arena::CreateMaybeMessage<::luckygoldProto::AllPlate>(Arena*);
template<> ::luckygoldProto::Block* Arena::CreateMaybeMessage<::luckygoldProto::Block>(Arena*);
template<> ::luckygoldProto::BonusInfo* Arena::CreateMaybeMessage<::luckygoldProto::BonusInfo>(Arena*);
template<> ::luckygoldProto::Cell* Arena::CreateMaybeMessage<::luckygoldProto::Cell>(Arena*);
template<> ::luckygoldProto::ColumnData* Arena::CreateMaybeMessage<::luckygoldProto::ColumnData>(Arena*);
template<> ::luckygoldProto::JpCurrency* Arena::CreateMaybeMessage<::luckygoldProto::JpCurrency>(Arena*);
template<> ::luckygoldProto::JpInfo* Arena::CreateMaybeMessage<::luckygoldProto::JpInfo>(Arena*);
template<> ::luckygoldProto::Plate* Arena::CreateMaybeMessage<::luckygoldProto::Plate>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace luckygoldProto {

enum Symbol : int {
  Wild = 0,
  Bonus = 1,
  Symbol1 = 2,
  Symbol2 = 3,
  Symbol3 = 4,
  Symbol4 = 5,
  Symbol5 = 6,
  Symbol6 = 7,
  Mix7 = 8,
  MixBar = 9,
  Symbol_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Symbol_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Symbol_IsValid(int value);
constexpr Symbol Symbol_MIN = Wild;
constexpr Symbol Symbol_MAX = MixBar;
constexpr int Symbol_ARRAYSIZE = Symbol_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Symbol_descriptor();
template<typename T>
inline const std::string& Symbol_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Symbol>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Symbol_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Symbol_descriptor(), enum_t_value);
}
inline bool Symbol_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Symbol* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Symbol>(
    Symbol_descriptor(), name, value);
}
// ===================================================================

class Block final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.Block) */ {
 public:
  inline Block() : Block(nullptr) {}
  ~Block() override;
  explicit PROTOBUF_CONSTEXPR Block(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Block(const Block& from);
  Block(Block&& from) noexcept
    : Block() {
    *this = ::std::move(from);
  }

  inline Block& operator=(const Block& from) {
    CopyFrom(from);
    return *this;
  }
  inline Block& operator=(Block&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Block& default_instance() {
    return *internal_default_instance();
  }
  static inline const Block* internal_default_instance() {
    return reinterpret_cast<const Block*>(
               &_Block_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Block& a, Block& b) {
    a.Swap(&b);
  }
  inline void Swap(Block* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Block* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Block* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Block>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Block& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Block& from) {
    Block::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Block* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.Block";
  }
  protected:
  explicit Block(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowFieldNumber = 1,
    kColumnFieldNumber = 2,
  };
  // uint32 row = 1;
  void clear_row();
  uint32_t row() const;
  void set_row(uint32_t value);
  private:
  uint32_t _internal_row() const;
  void _internal_set_row(uint32_t value);
  public:

  // uint32 column = 2;
  void clear_column();
  uint32_t column() const;
  void set_column(uint32_t value);
  private:
  uint32_t _internal_column() const;
  void _internal_set_column(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.Block)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint32_t row_;
    uint32_t column_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class Cell final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.Cell) */ {
 public:
  inline Cell() : Cell(nullptr) {}
  ~Cell() override;
  explicit PROTOBUF_CONSTEXPR Cell(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Cell(const Cell& from);
  Cell(Cell&& from) noexcept
    : Cell() {
    *this = ::std::move(from);
  }

  inline Cell& operator=(const Cell& from) {
    CopyFrom(from);
    return *this;
  }
  inline Cell& operator=(Cell&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Cell& default_instance() {
    return *internal_default_instance();
  }
  static inline const Cell* internal_default_instance() {
    return reinterpret_cast<const Cell*>(
               &_Cell_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Cell& a, Cell& b) {
    a.Swap(&b);
  }
  inline void Swap(Cell* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Cell* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Cell* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Cell>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Cell& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Cell& from) {
    Cell::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Cell* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.Cell";
  }
  protected:
  explicit Cell(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSymbolFieldNumber = 1,
    kIsConnectFieldNumber = 2,
  };
  // .luckygoldProto.Symbol symbol = 1;
  void clear_symbol();
  ::luckygoldProto::Symbol symbol() const;
  void set_symbol(::luckygoldProto::Symbol value);
  private:
  ::luckygoldProto::Symbol _internal_symbol() const;
  void _internal_set_symbol(::luckygoldProto::Symbol value);
  public:

  // bool isConnect = 2;
  void clear_isconnect();
  bool isconnect() const;
  void set_isconnect(bool value);
  private:
  bool _internal_isconnect() const;
  void _internal_set_isconnect(bool value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.Cell)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int symbol_;
    bool isconnect_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class ColumnData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.ColumnData) */ {
 public:
  inline ColumnData() : ColumnData(nullptr) {}
  ~ColumnData() override;
  explicit PROTOBUF_CONSTEXPR ColumnData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ColumnData(const ColumnData& from);
  ColumnData(ColumnData&& from) noexcept
    : ColumnData() {
    *this = ::std::move(from);
  }

  inline ColumnData& operator=(const ColumnData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ColumnData& operator=(ColumnData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ColumnData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ColumnData* internal_default_instance() {
    return reinterpret_cast<const ColumnData*>(
               &_ColumnData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ColumnData& a, ColumnData& b) {
    a.Swap(&b);
  }
  inline void Swap(ColumnData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ColumnData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ColumnData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ColumnData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ColumnData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ColumnData& from) {
    ColumnData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ColumnData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.ColumnData";
  }
  protected:
  explicit ColumnData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowFieldNumber = 1,
  };
  // repeated .luckygoldProto.Cell row = 1;
  int row_size() const;
  private:
  int _internal_row_size() const;
  public:
  void clear_row();
  ::luckygoldProto::Cell* mutable_row(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Cell >*
      mutable_row();
  private:
  const ::luckygoldProto::Cell& _internal_row(int index) const;
  ::luckygoldProto::Cell* _internal_add_row();
  public:
  const ::luckygoldProto::Cell& row(int index) const;
  ::luckygoldProto::Cell* add_row();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Cell >&
      row() const;

  // @@protoc_insertion_point(class_scope:luckygoldProto.ColumnData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Cell > row_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class Plate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.Plate) */ {
 public:
  inline Plate() : Plate(nullptr) {}
  ~Plate() override;
  explicit PROTOBUF_CONSTEXPR Plate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Plate(const Plate& from);
  Plate(Plate&& from) noexcept
    : Plate() {
    *this = ::std::move(from);
  }

  inline Plate& operator=(const Plate& from) {
    CopyFrom(from);
    return *this;
  }
  inline Plate& operator=(Plate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Plate& default_instance() {
    return *internal_default_instance();
  }
  static inline const Plate* internal_default_instance() {
    return reinterpret_cast<const Plate*>(
               &_Plate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Plate& a, Plate& b) {
    a.Swap(&b);
  }
  inline void Swap(Plate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Plate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Plate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Plate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Plate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Plate& from) {
    Plate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Plate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.Plate";
  }
  protected:
  explicit Plate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kColumnFieldNumber = 1,
    kGoldFieldNumber = 3,
    kJpFieldNumber = 5,
    kWinFieldNumber = 2,
    kGoldWinFieldNumber = 4,
  };
  // repeated .luckygoldProto.ColumnData column = 1;
  int column_size() const;
  private:
  int _internal_column_size() const;
  public:
  void clear_column();
  ::luckygoldProto::ColumnData* mutable_column(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::ColumnData >*
      mutable_column();
  private:
  const ::luckygoldProto::ColumnData& _internal_column(int index) const;
  ::luckygoldProto::ColumnData* _internal_add_column();
  public:
  const ::luckygoldProto::ColumnData& column(int index) const;
  ::luckygoldProto::ColumnData* add_column();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::ColumnData >&
      column() const;

  // .luckygoldProto.BonusInfo gold = 3;
  bool has_gold() const;
  private:
  bool _internal_has_gold() const;
  public:
  void clear_gold();
  const ::luckygoldProto::BonusInfo& gold() const;
  PROTOBUF_NODISCARD ::luckygoldProto::BonusInfo* release_gold();
  ::luckygoldProto::BonusInfo* mutable_gold();
  void set_allocated_gold(::luckygoldProto::BonusInfo* gold);
  private:
  const ::luckygoldProto::BonusInfo& _internal_gold() const;
  ::luckygoldProto::BonusInfo* _internal_mutable_gold();
  public:
  void unsafe_arena_set_allocated_gold(
      ::luckygoldProto::BonusInfo* gold);
  ::luckygoldProto::BonusInfo* unsafe_arena_release_gold();

  // .luckygoldProto.JpInfo Jp = 5;
  bool has_jp() const;
  private:
  bool _internal_has_jp() const;
  public:
  void clear_jp();
  const ::luckygoldProto::JpInfo& jp() const;
  PROTOBUF_NODISCARD ::luckygoldProto::JpInfo* release_jp();
  ::luckygoldProto::JpInfo* mutable_jp();
  void set_allocated_jp(::luckygoldProto::JpInfo* jp);
  private:
  const ::luckygoldProto::JpInfo& _internal_jp() const;
  ::luckygoldProto::JpInfo* _internal_mutable_jp();
  public:
  void unsafe_arena_set_allocated_jp(
      ::luckygoldProto::JpInfo* jp);
  ::luckygoldProto::JpInfo* unsafe_arena_release_jp();

  // double win = 2;
  void clear_win();
  double win() const;
  void set_win(double value);
  private:
  double _internal_win() const;
  void _internal_set_win(double value);
  public:

  // double goldWin = 4;
  void clear_goldwin();
  double goldwin() const;
  void set_goldwin(double value);
  private:
  double _internal_goldwin() const;
  void _internal_set_goldwin(double value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.Plate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::ColumnData > column_;
    ::luckygoldProto::BonusInfo* gold_;
    ::luckygoldProto::JpInfo* jp_;
    double win_;
    double goldwin_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class BonusInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.BonusInfo) */ {
 public:
  inline BonusInfo() : BonusInfo(nullptr) {}
  ~BonusInfo() override;
  explicit PROTOBUF_CONSTEXPR BonusInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BonusInfo(const BonusInfo& from);
  BonusInfo(BonusInfo&& from) noexcept
    : BonusInfo() {
    *this = ::std::move(from);
  }

  inline BonusInfo& operator=(const BonusInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline BonusInfo& operator=(BonusInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BonusInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const BonusInfo* internal_default_instance() {
    return reinterpret_cast<const BonusInfo*>(
               &_BonusInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(BonusInfo& a, BonusInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(BonusInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BonusInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BonusInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BonusInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BonusInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BonusInfo& from) {
    BonusInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BonusInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.BonusInfo";
  }
  protected:
  explicit BonusInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGoldFieldNumber = 3,
    kBarWinFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // repeated double gold = 3;
  int gold_size() const;
  private:
  int _internal_gold_size() const;
  public:
  void clear_gold();
  private:
  double _internal_gold(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_gold() const;
  void _internal_add_gold(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_gold();
  public:
  double gold(int index) const;
  void set_gold(int index, double value);
  void add_gold(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      gold() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_gold();

  // double barWin = 1;
  void clear_barwin();
  double barwin() const;
  void set_barwin(double value);
  private:
  double _internal_barwin() const;
  void _internal_set_barwin(double value);
  public:

  // int64 count = 2;
  void clear_count();
  int64_t count() const;
  void set_count(int64_t value);
  private:
  int64_t _internal_count() const;
  void _internal_set_count(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.BonusInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > gold_;
    double barwin_;
    int64_t count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class JpInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.JpInfo) */ {
 public:
  inline JpInfo() : JpInfo(nullptr) {}
  ~JpInfo() override;
  explicit PROTOBUF_CONSTEXPR JpInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JpInfo(const JpInfo& from);
  JpInfo(JpInfo&& from) noexcept
    : JpInfo() {
    *this = ::std::move(from);
  }

  inline JpInfo& operator=(const JpInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline JpInfo& operator=(JpInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JpInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const JpInfo* internal_default_instance() {
    return reinterpret_cast<const JpInfo*>(
               &_JpInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(JpInfo& a, JpInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(JpInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JpInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JpInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JpInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JpInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const JpInfo& from) {
    JpInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JpInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.JpInfo";
  }
  protected:
  explicit JpInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWinFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // double win = 2;
  void clear_win();
  double win() const;
  void set_win(double value);
  private:
  double _internal_win() const;
  void _internal_set_win(double value);
  public:

  // int32 id = 1;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.JpInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double win_;
    int32_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class JpCurrency final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.JpCurrency) */ {
 public:
  inline JpCurrency() : JpCurrency(nullptr) {}
  ~JpCurrency() override;
  explicit PROTOBUF_CONSTEXPR JpCurrency(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JpCurrency(const JpCurrency& from);
  JpCurrency(JpCurrency&& from) noexcept
    : JpCurrency() {
    *this = ::std::move(from);
  }

  inline JpCurrency& operator=(const JpCurrency& from) {
    CopyFrom(from);
    return *this;
  }
  inline JpCurrency& operator=(JpCurrency&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JpCurrency& default_instance() {
    return *internal_default_instance();
  }
  static inline const JpCurrency* internal_default_instance() {
    return reinterpret_cast<const JpCurrency*>(
               &_JpCurrency_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(JpCurrency& a, JpCurrency& b) {
    a.Swap(&b);
  }
  inline void Swap(JpCurrency* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JpCurrency* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JpCurrency* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JpCurrency>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JpCurrency& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const JpCurrency& from) {
    JpCurrency::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JpCurrency* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.JpCurrency";
  }
  protected:
  explicit JpCurrency(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInfoFieldNumber = 2,
    kCurrencyFieldNumber = 1,
  };
  // repeated .luckygoldProto.JpInfo info = 2;
  int info_size() const;
  private:
  int _internal_info_size() const;
  public:
  void clear_info();
  ::luckygoldProto::JpInfo* mutable_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpInfo >*
      mutable_info();
  private:
  const ::luckygoldProto::JpInfo& _internal_info(int index) const;
  ::luckygoldProto::JpInfo* _internal_add_info();
  public:
  const ::luckygoldProto::JpInfo& info(int index) const;
  ::luckygoldProto::JpInfo* add_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpInfo >&
      info() const;

  // int32 currency = 1;
  void clear_currency();
  int32_t currency() const;
  void set_currency(int32_t value);
  private:
  int32_t _internal_currency() const;
  void _internal_set_currency(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:luckygoldProto.JpCurrency)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpInfo > info_;
    int32_t currency_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// -------------------------------------------------------------------

class AllPlate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:luckygoldProto.AllPlate) */ {
 public:
  inline AllPlate() : AllPlate(nullptr) {}
  ~AllPlate() override;
  explicit PROTOBUF_CONSTEXPR AllPlate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AllPlate(const AllPlate& from);
  AllPlate(AllPlate&& from) noexcept
    : AllPlate() {
    *this = ::std::move(from);
  }

  inline AllPlate& operator=(const AllPlate& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllPlate& operator=(AllPlate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AllPlate& default_instance() {
    return *internal_default_instance();
  }
  static inline const AllPlate* internal_default_instance() {
    return reinterpret_cast<const AllPlate*>(
               &_AllPlate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AllPlate& a, AllPlate& b) {
    a.Swap(&b);
  }
  inline void Swap(AllPlate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllPlate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AllPlate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AllPlate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AllPlate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AllPlate& from) {
    AllPlate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllPlate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "luckygoldProto.AllPlate";
  }
  protected:
  explicit AllPlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlateFieldNumber = 1,
    kJpFieldNumber = 2,
  };
  // repeated .luckygoldProto.Plate plate = 1;
  int plate_size() const;
  private:
  int _internal_plate_size() const;
  public:
  void clear_plate();
  ::luckygoldProto::Plate* mutable_plate(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Plate >*
      mutable_plate();
  private:
  const ::luckygoldProto::Plate& _internal_plate(int index) const;
  ::luckygoldProto::Plate* _internal_add_plate();
  public:
  const ::luckygoldProto::Plate& plate(int index) const;
  ::luckygoldProto::Plate* add_plate();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Plate >&
      plate() const;

  // repeated .luckygoldProto.JpCurrency jp = 2;
  int jp_size() const;
  private:
  int _internal_jp_size() const;
  public:
  void clear_jp();
  ::luckygoldProto::JpCurrency* mutable_jp(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpCurrency >*
      mutable_jp();
  private:
  const ::luckygoldProto::JpCurrency& _internal_jp(int index) const;
  ::luckygoldProto::JpCurrency* _internal_add_jp();
  public:
  const ::luckygoldProto::JpCurrency& jp(int index) const;
  ::luckygoldProto::JpCurrency* add_jp();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpCurrency >&
      jp() const;

  // @@protoc_insertion_point(class_scope:luckygoldProto.AllPlate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Plate > plate_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpCurrency > jp_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_luckygoldbricks_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Block

// uint32 row = 1;
inline void Block::clear_row() {
  _impl_.row_ = 0u;
}
inline uint32_t Block::_internal_row() const {
  return _impl_.row_;
}
inline uint32_t Block::row() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Block.row)
  return _internal_row();
}
inline void Block::_internal_set_row(uint32_t value) {
  
  _impl_.row_ = value;
}
inline void Block::set_row(uint32_t value) {
  _internal_set_row(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Block.row)
}

// uint32 column = 2;
inline void Block::clear_column() {
  _impl_.column_ = 0u;
}
inline uint32_t Block::_internal_column() const {
  return _impl_.column_;
}
inline uint32_t Block::column() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Block.column)
  return _internal_column();
}
inline void Block::_internal_set_column(uint32_t value) {
  
  _impl_.column_ = value;
}
inline void Block::set_column(uint32_t value) {
  _internal_set_column(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Block.column)
}

// -------------------------------------------------------------------

// Cell

// .luckygoldProto.Symbol symbol = 1;
inline void Cell::clear_symbol() {
  _impl_.symbol_ = 0;
}
inline ::luckygoldProto::Symbol Cell::_internal_symbol() const {
  return static_cast< ::luckygoldProto::Symbol >(_impl_.symbol_);
}
inline ::luckygoldProto::Symbol Cell::symbol() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Cell.symbol)
  return _internal_symbol();
}
inline void Cell::_internal_set_symbol(::luckygoldProto::Symbol value) {
  
  _impl_.symbol_ = value;
}
inline void Cell::set_symbol(::luckygoldProto::Symbol value) {
  _internal_set_symbol(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Cell.symbol)
}

// bool isConnect = 2;
inline void Cell::clear_isconnect() {
  _impl_.isconnect_ = false;
}
inline bool Cell::_internal_isconnect() const {
  return _impl_.isconnect_;
}
inline bool Cell::isconnect() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Cell.isConnect)
  return _internal_isconnect();
}
inline void Cell::_internal_set_isconnect(bool value) {
  
  _impl_.isconnect_ = value;
}
inline void Cell::set_isconnect(bool value) {
  _internal_set_isconnect(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Cell.isConnect)
}

// -------------------------------------------------------------------

// ColumnData

// repeated .luckygoldProto.Cell row = 1;
inline int ColumnData::_internal_row_size() const {
  return _impl_.row_.size();
}
inline int ColumnData::row_size() const {
  return _internal_row_size();
}
inline void ColumnData::clear_row() {
  _impl_.row_.Clear();
}
inline ::luckygoldProto::Cell* ColumnData::mutable_row(int index) {
  // @@protoc_insertion_point(field_mutable:luckygoldProto.ColumnData.row)
  return _impl_.row_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Cell >*
ColumnData::mutable_row() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.ColumnData.row)
  return &_impl_.row_;
}
inline const ::luckygoldProto::Cell& ColumnData::_internal_row(int index) const {
  return _impl_.row_.Get(index);
}
inline const ::luckygoldProto::Cell& ColumnData::row(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.ColumnData.row)
  return _internal_row(index);
}
inline ::luckygoldProto::Cell* ColumnData::_internal_add_row() {
  return _impl_.row_.Add();
}
inline ::luckygoldProto::Cell* ColumnData::add_row() {
  ::luckygoldProto::Cell* _add = _internal_add_row();
  // @@protoc_insertion_point(field_add:luckygoldProto.ColumnData.row)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Cell >&
ColumnData::row() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.ColumnData.row)
  return _impl_.row_;
}

// -------------------------------------------------------------------

// Plate

// repeated .luckygoldProto.ColumnData column = 1;
inline int Plate::_internal_column_size() const {
  return _impl_.column_.size();
}
inline int Plate::column_size() const {
  return _internal_column_size();
}
inline void Plate::clear_column() {
  _impl_.column_.Clear();
}
inline ::luckygoldProto::ColumnData* Plate::mutable_column(int index) {
  // @@protoc_insertion_point(field_mutable:luckygoldProto.Plate.column)
  return _impl_.column_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::ColumnData >*
Plate::mutable_column() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.Plate.column)
  return &_impl_.column_;
}
inline const ::luckygoldProto::ColumnData& Plate::_internal_column(int index) const {
  return _impl_.column_.Get(index);
}
inline const ::luckygoldProto::ColumnData& Plate::column(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Plate.column)
  return _internal_column(index);
}
inline ::luckygoldProto::ColumnData* Plate::_internal_add_column() {
  return _impl_.column_.Add();
}
inline ::luckygoldProto::ColumnData* Plate::add_column() {
  ::luckygoldProto::ColumnData* _add = _internal_add_column();
  // @@protoc_insertion_point(field_add:luckygoldProto.Plate.column)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::ColumnData >&
Plate::column() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.Plate.column)
  return _impl_.column_;
}

// double win = 2;
inline void Plate::clear_win() {
  _impl_.win_ = 0;
}
inline double Plate::_internal_win() const {
  return _impl_.win_;
}
inline double Plate::win() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Plate.win)
  return _internal_win();
}
inline void Plate::_internal_set_win(double value) {
  
  _impl_.win_ = value;
}
inline void Plate::set_win(double value) {
  _internal_set_win(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Plate.win)
}

// .luckygoldProto.BonusInfo gold = 3;
inline bool Plate::_internal_has_gold() const {
  return this != internal_default_instance() && _impl_.gold_ != nullptr;
}
inline bool Plate::has_gold() const {
  return _internal_has_gold();
}
inline void Plate::clear_gold() {
  if (GetArenaForAllocation() == nullptr && _impl_.gold_ != nullptr) {
    delete _impl_.gold_;
  }
  _impl_.gold_ = nullptr;
}
inline const ::luckygoldProto::BonusInfo& Plate::_internal_gold() const {
  const ::luckygoldProto::BonusInfo* p = _impl_.gold_;
  return p != nullptr ? *p : reinterpret_cast<const ::luckygoldProto::BonusInfo&>(
      ::luckygoldProto::_BonusInfo_default_instance_);
}
inline const ::luckygoldProto::BonusInfo& Plate::gold() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Plate.gold)
  return _internal_gold();
}
inline void Plate::unsafe_arena_set_allocated_gold(
    ::luckygoldProto::BonusInfo* gold) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.gold_);
  }
  _impl_.gold_ = gold;
  if (gold) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:luckygoldProto.Plate.gold)
}
inline ::luckygoldProto::BonusInfo* Plate::release_gold() {
  
  ::luckygoldProto::BonusInfo* temp = _impl_.gold_;
  _impl_.gold_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::luckygoldProto::BonusInfo* Plate::unsafe_arena_release_gold() {
  // @@protoc_insertion_point(field_release:luckygoldProto.Plate.gold)
  
  ::luckygoldProto::BonusInfo* temp = _impl_.gold_;
  _impl_.gold_ = nullptr;
  return temp;
}
inline ::luckygoldProto::BonusInfo* Plate::_internal_mutable_gold() {
  
  if (_impl_.gold_ == nullptr) {
    auto* p = CreateMaybeMessage<::luckygoldProto::BonusInfo>(GetArenaForAllocation());
    _impl_.gold_ = p;
  }
  return _impl_.gold_;
}
inline ::luckygoldProto::BonusInfo* Plate::mutable_gold() {
  ::luckygoldProto::BonusInfo* _msg = _internal_mutable_gold();
  // @@protoc_insertion_point(field_mutable:luckygoldProto.Plate.gold)
  return _msg;
}
inline void Plate::set_allocated_gold(::luckygoldProto::BonusInfo* gold) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.gold_;
  }
  if (gold) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(gold);
    if (message_arena != submessage_arena) {
      gold = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gold, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.gold_ = gold;
  // @@protoc_insertion_point(field_set_allocated:luckygoldProto.Plate.gold)
}

// double goldWin = 4;
inline void Plate::clear_goldwin() {
  _impl_.goldwin_ = 0;
}
inline double Plate::_internal_goldwin() const {
  return _impl_.goldwin_;
}
inline double Plate::goldwin() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Plate.goldWin)
  return _internal_goldwin();
}
inline void Plate::_internal_set_goldwin(double value) {
  
  _impl_.goldwin_ = value;
}
inline void Plate::set_goldwin(double value) {
  _internal_set_goldwin(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.Plate.goldWin)
}

// .luckygoldProto.JpInfo Jp = 5;
inline bool Plate::_internal_has_jp() const {
  return this != internal_default_instance() && _impl_.jp_ != nullptr;
}
inline bool Plate::has_jp() const {
  return _internal_has_jp();
}
inline void Plate::clear_jp() {
  if (GetArenaForAllocation() == nullptr && _impl_.jp_ != nullptr) {
    delete _impl_.jp_;
  }
  _impl_.jp_ = nullptr;
}
inline const ::luckygoldProto::JpInfo& Plate::_internal_jp() const {
  const ::luckygoldProto::JpInfo* p = _impl_.jp_;
  return p != nullptr ? *p : reinterpret_cast<const ::luckygoldProto::JpInfo&>(
      ::luckygoldProto::_JpInfo_default_instance_);
}
inline const ::luckygoldProto::JpInfo& Plate::jp() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.Plate.Jp)
  return _internal_jp();
}
inline void Plate::unsafe_arena_set_allocated_jp(
    ::luckygoldProto::JpInfo* jp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.jp_);
  }
  _impl_.jp_ = jp;
  if (jp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:luckygoldProto.Plate.Jp)
}
inline ::luckygoldProto::JpInfo* Plate::release_jp() {
  
  ::luckygoldProto::JpInfo* temp = _impl_.jp_;
  _impl_.jp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::luckygoldProto::JpInfo* Plate::unsafe_arena_release_jp() {
  // @@protoc_insertion_point(field_release:luckygoldProto.Plate.Jp)
  
  ::luckygoldProto::JpInfo* temp = _impl_.jp_;
  _impl_.jp_ = nullptr;
  return temp;
}
inline ::luckygoldProto::JpInfo* Plate::_internal_mutable_jp() {
  
  if (_impl_.jp_ == nullptr) {
    auto* p = CreateMaybeMessage<::luckygoldProto::JpInfo>(GetArenaForAllocation());
    _impl_.jp_ = p;
  }
  return _impl_.jp_;
}
inline ::luckygoldProto::JpInfo* Plate::mutable_jp() {
  ::luckygoldProto::JpInfo* _msg = _internal_mutable_jp();
  // @@protoc_insertion_point(field_mutable:luckygoldProto.Plate.Jp)
  return _msg;
}
inline void Plate::set_allocated_jp(::luckygoldProto::JpInfo* jp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.jp_;
  }
  if (jp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(jp);
    if (message_arena != submessage_arena) {
      jp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jp, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.jp_ = jp;
  // @@protoc_insertion_point(field_set_allocated:luckygoldProto.Plate.Jp)
}

// -------------------------------------------------------------------

// BonusInfo

// double barWin = 1;
inline void BonusInfo::clear_barwin() {
  _impl_.barwin_ = 0;
}
inline double BonusInfo::_internal_barwin() const {
  return _impl_.barwin_;
}
inline double BonusInfo::barwin() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.BonusInfo.barWin)
  return _internal_barwin();
}
inline void BonusInfo::_internal_set_barwin(double value) {
  
  _impl_.barwin_ = value;
}
inline void BonusInfo::set_barwin(double value) {
  _internal_set_barwin(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.BonusInfo.barWin)
}

// int64 count = 2;
inline void BonusInfo::clear_count() {
  _impl_.count_ = int64_t{0};
}
inline int64_t BonusInfo::_internal_count() const {
  return _impl_.count_;
}
inline int64_t BonusInfo::count() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.BonusInfo.count)
  return _internal_count();
}
inline void BonusInfo::_internal_set_count(int64_t value) {
  
  _impl_.count_ = value;
}
inline void BonusInfo::set_count(int64_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.BonusInfo.count)
}

// repeated double gold = 3;
inline int BonusInfo::_internal_gold_size() const {
  return _impl_.gold_.size();
}
inline int BonusInfo::gold_size() const {
  return _internal_gold_size();
}
inline void BonusInfo::clear_gold() {
  _impl_.gold_.Clear();
}
inline double BonusInfo::_internal_gold(int index) const {
  return _impl_.gold_.Get(index);
}
inline double BonusInfo::gold(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.BonusInfo.gold)
  return _internal_gold(index);
}
inline void BonusInfo::set_gold(int index, double value) {
  _impl_.gold_.Set(index, value);
  // @@protoc_insertion_point(field_set:luckygoldProto.BonusInfo.gold)
}
inline void BonusInfo::_internal_add_gold(double value) {
  _impl_.gold_.Add(value);
}
inline void BonusInfo::add_gold(double value) {
  _internal_add_gold(value);
  // @@protoc_insertion_point(field_add:luckygoldProto.BonusInfo.gold)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
BonusInfo::_internal_gold() const {
  return _impl_.gold_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
BonusInfo::gold() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.BonusInfo.gold)
  return _internal_gold();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
BonusInfo::_internal_mutable_gold() {
  return &_impl_.gold_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
BonusInfo::mutable_gold() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.BonusInfo.gold)
  return _internal_mutable_gold();
}

// -------------------------------------------------------------------

// JpInfo

// int32 id = 1;
inline void JpInfo::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t JpInfo::_internal_id() const {
  return _impl_.id_;
}
inline int32_t JpInfo::id() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.JpInfo.id)
  return _internal_id();
}
inline void JpInfo::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void JpInfo::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.JpInfo.id)
}

// double win = 2;
inline void JpInfo::clear_win() {
  _impl_.win_ = 0;
}
inline double JpInfo::_internal_win() const {
  return _impl_.win_;
}
inline double JpInfo::win() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.JpInfo.win)
  return _internal_win();
}
inline void JpInfo::_internal_set_win(double value) {
  
  _impl_.win_ = value;
}
inline void JpInfo::set_win(double value) {
  _internal_set_win(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.JpInfo.win)
}

// -------------------------------------------------------------------

// JpCurrency

// int32 currency = 1;
inline void JpCurrency::clear_currency() {
  _impl_.currency_ = 0;
}
inline int32_t JpCurrency::_internal_currency() const {
  return _impl_.currency_;
}
inline int32_t JpCurrency::currency() const {
  // @@protoc_insertion_point(field_get:luckygoldProto.JpCurrency.currency)
  return _internal_currency();
}
inline void JpCurrency::_internal_set_currency(int32_t value) {
  
  _impl_.currency_ = value;
}
inline void JpCurrency::set_currency(int32_t value) {
  _internal_set_currency(value);
  // @@protoc_insertion_point(field_set:luckygoldProto.JpCurrency.currency)
}

// repeated .luckygoldProto.JpInfo info = 2;
inline int JpCurrency::_internal_info_size() const {
  return _impl_.info_.size();
}
inline int JpCurrency::info_size() const {
  return _internal_info_size();
}
inline void JpCurrency::clear_info() {
  _impl_.info_.Clear();
}
inline ::luckygoldProto::JpInfo* JpCurrency::mutable_info(int index) {
  // @@protoc_insertion_point(field_mutable:luckygoldProto.JpCurrency.info)
  return _impl_.info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpInfo >*
JpCurrency::mutable_info() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.JpCurrency.info)
  return &_impl_.info_;
}
inline const ::luckygoldProto::JpInfo& JpCurrency::_internal_info(int index) const {
  return _impl_.info_.Get(index);
}
inline const ::luckygoldProto::JpInfo& JpCurrency::info(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.JpCurrency.info)
  return _internal_info(index);
}
inline ::luckygoldProto::JpInfo* JpCurrency::_internal_add_info() {
  return _impl_.info_.Add();
}
inline ::luckygoldProto::JpInfo* JpCurrency::add_info() {
  ::luckygoldProto::JpInfo* _add = _internal_add_info();
  // @@protoc_insertion_point(field_add:luckygoldProto.JpCurrency.info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpInfo >&
JpCurrency::info() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.JpCurrency.info)
  return _impl_.info_;
}

// -------------------------------------------------------------------

// AllPlate

// repeated .luckygoldProto.Plate plate = 1;
inline int AllPlate::_internal_plate_size() const {
  return _impl_.plate_.size();
}
inline int AllPlate::plate_size() const {
  return _internal_plate_size();
}
inline void AllPlate::clear_plate() {
  _impl_.plate_.Clear();
}
inline ::luckygoldProto::Plate* AllPlate::mutable_plate(int index) {
  // @@protoc_insertion_point(field_mutable:luckygoldProto.AllPlate.plate)
  return _impl_.plate_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Plate >*
AllPlate::mutable_plate() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.AllPlate.plate)
  return &_impl_.plate_;
}
inline const ::luckygoldProto::Plate& AllPlate::_internal_plate(int index) const {
  return _impl_.plate_.Get(index);
}
inline const ::luckygoldProto::Plate& AllPlate::plate(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.AllPlate.plate)
  return _internal_plate(index);
}
inline ::luckygoldProto::Plate* AllPlate::_internal_add_plate() {
  return _impl_.plate_.Add();
}
inline ::luckygoldProto::Plate* AllPlate::add_plate() {
  ::luckygoldProto::Plate* _add = _internal_add_plate();
  // @@protoc_insertion_point(field_add:luckygoldProto.AllPlate.plate)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::Plate >&
AllPlate::plate() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.AllPlate.plate)
  return _impl_.plate_;
}

// repeated .luckygoldProto.JpCurrency jp = 2;
inline int AllPlate::_internal_jp_size() const {
  return _impl_.jp_.size();
}
inline int AllPlate::jp_size() const {
  return _internal_jp_size();
}
inline void AllPlate::clear_jp() {
  _impl_.jp_.Clear();
}
inline ::luckygoldProto::JpCurrency* AllPlate::mutable_jp(int index) {
  // @@protoc_insertion_point(field_mutable:luckygoldProto.AllPlate.jp)
  return _impl_.jp_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpCurrency >*
AllPlate::mutable_jp() {
  // @@protoc_insertion_point(field_mutable_list:luckygoldProto.AllPlate.jp)
  return &_impl_.jp_;
}
inline const ::luckygoldProto::JpCurrency& AllPlate::_internal_jp(int index) const {
  return _impl_.jp_.Get(index);
}
inline const ::luckygoldProto::JpCurrency& AllPlate::jp(int index) const {
  // @@protoc_insertion_point(field_get:luckygoldProto.AllPlate.jp)
  return _internal_jp(index);
}
inline ::luckygoldProto::JpCurrency* AllPlate::_internal_add_jp() {
  return _impl_.jp_.Add();
}
inline ::luckygoldProto::JpCurrency* AllPlate::add_jp() {
  ::luckygoldProto::JpCurrency* _add = _internal_add_jp();
  // @@protoc_insertion_point(field_add:luckygoldProto.AllPlate.jp)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::luckygoldProto::JpCurrency >&
AllPlate::jp() const {
  // @@protoc_insertion_point(field_list:luckygoldProto.AllPlate.jp)
  return _impl_.jp_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace luckygoldProto

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::luckygoldProto::Symbol> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::luckygoldProto::Symbol>() {
  return ::luckygoldProto::Symbol_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_luckygoldbricks_2eproto
